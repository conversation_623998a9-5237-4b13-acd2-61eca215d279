import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_procurement/app/invoices/presentation/widgets/invoice_summary.dart';
import 'package:td_procurement/app/order/domain/entities/order_params.dart';
import 'package:td_procurement/app/order/presentation/controllers/cart_notifier.dart';
import 'package:td_procurement/app/order/presentation/widgets/cart_badge.dart';
import 'package:td_procurement/app/settings/presentation/widgets/settings.dart';
import 'package:td_procurement/app/shell/data/models/global_search.model.dart';
import 'package:td_procurement/app/shell/domain/use_cases/shell_use_cases.dart';
import 'package:td_procurement/core/models/sized_item.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class TopBar extends ConsumerStatefulWidget {
  const TopBar({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => TopBarState();
}

class TopBarState extends ConsumerState<TopBar> {
  final GlobalKey buttonKey = GlobalKey();
  final _controller = TextEditingController();
  OverlayEntry? _overlayEntry;
  final consoleShellLayerLink = LayerLink();
  final _deBouncer = DeBouncer(milliseconds: 500);
  GlobalSearch? searchData;
  final ValueNotifier<bool> _loading = ValueNotifier(false);
  final FocusNode _focusNode = FocusNode();

  // late bool isMasterDistributor = ref.read(isMasterDistributorProvider);

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final cartItems = ref.watch(cartProvider).uniqueCartItemsWithNonZeroCount;

    final featuresEnabled = {
      AuthScope.salesOrder:
          ref.watch(isFeatureEnabledProvider(AuthScope.salesOrder)),
      AuthScope.purchaseOrder:
          ref.watch(isFeatureEnabledProvider(AuthScope.purchaseOrder)),
      AuthScope.globalOrder:
          ref.watch(isFeatureEnabledProvider(AuthScope.globalOrder)),
      AuthScope.payout: ref.watch(isFeatureEnabledProvider(AuthScope.payout)),
    };

    return SizedBox(
      height: 70,
      child: DecoratedBox(
        decoration: BoxDecoration(color: Palette.kFCFCFC),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: CompositedTransformTarget(
                link: consoleShellLayerLink,
                child: Material(
                  elevation: 5,
                  color: Colors.transparent,
                  shadowColor: Palette.k0000000A,
                  child: TextField(
                    readOnly: false,
                    controller: _controller,
                    onTap: () => _showBranchPickerOverlay(context),
                    decoration: InputDecoration(
                        hintText: 'Search',
                        prefixIcon: Padding(
                          padding: const EdgeInsets.only(top: 2.0),
                          child: SvgPicture.asset(
                            kSearchSvg,
                            fit: BoxFit.scaleDown,
                            width: 16,
                            height: 16,
                          ),
                        ),
                        constraints: BoxConstraints.tight(
                          const Size(double.maxFinite, 40),
                        ),
                        filled: true,
                        fillColor: Colors.white),
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlignVertical: TextAlignVertical.center,
                    cursorColor: Palette.strokePressed,
                    cursorHeight: 14,
                  ),
                ),
              ),
            ),
            const Gap(40),
            IconButton(
              onPressed: () {
                showCustomGeneralDialog(
                  context,
                  dismissible: true,
                  percentage: 0.35,
                  child: const Settings(),
                );
              },
              icon: SvgPicture.asset(kSettingsSvg),
            ),
            const Gap(5),
            IconButton(
              icon: SvgPicture.asset(kBellSvg),
              onPressed: () {},
            ),
            const Gap(14),
            if (cartItems.isNotEmpty)
              const CartBadgeWidget()
            else if ((featuresEnabled[AuthScope.salesOrder]! &&
                (featuresEnabled[AuthScope.purchaseOrder]! ||
                    featuresEnabled[AuthScope.globalOrder]!)))
              _buildDropdownOrderButton(context)
            else if (featuresEnabled[AuthScope.purchaseOrder]! ||
                featuresEnabled[AuthScope.globalOrder]!)
              ElevatedButton(
                onPressed: () => context.pushNamed(kCreateOrderRoute),
                child: const Text('New Order'),
              ),
            const Gap(30),
          ],
        ),
      ),
    );
  }

  Widget _buildDropdownOrderButton(BuildContext context) {
    return ConstrainedBox(
      constraints: const BoxConstraints(
        minWidth: 116,
        minHeight: 40,
        maxHeight: 40,
      ),
      child: ElevatedButton(
        key: buttonKey,
        onPressed: () {
          final RenderBox buttonBox =
              buttonKey.currentContext!.findRenderObject() as RenderBox;
          final Offset buttonPosition = buttonBox.localToGlobal(Offset.zero);
          final Size buttonSize = buttonBox.size;

          showMenu(
            context: context,
            color: Colors.white,
            position: RelativeRect.fromLTRB(
              buttonPosition.dx,
              buttonPosition.dy + buttonSize.height + 2,
              buttonPosition.dx + buttonSize.width,
              buttonPosition.dy,
            ),
            items: [
              const PopupMenuItem(
                value: OrderType.purchase,
                child: Text('Create Purchase Order'),
              ),
              const PopupMenuItem(
                value: OrderType.sales,
                child: Text('Create Sales Order'),
              ),
              const PopupMenuItem(
                value: OrderType.advanceInvoice,
                child: Text('Create Advance Invoice'),
              ),
            ],
          ).then((value) {
            if (!context.mounted) return;
            if (value == OrderType.purchase) {
              context.pushNamed(kCreateOrderRoute);
            } else if (value == OrderType.sales) {
              ref.read(cartProvider.notifier).clearCart();
              context.pushNamed(kCreateSalesOrderRoute);
              // saveRouteTypeToStorage();
            } else if (value == OrderType.advanceInvoice) {
              context.pushNamed(kCreateAdvanceInvoiceRoute);
            }
          });
        },
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        child: const Text('New Order'),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  // void saveRouteTypeToStorage() async {
  //   final sp = await SharedPreferences.getInstance();
  //   sp.setBool(StorageKeys.isSalesOrderPath, true);
  // }

  // Show overlay for branches
  void _showBranchPickerOverlay(BuildContext context) {
    _overlayEntry = _createOverlayEntry(
      context,
      SizedItem(width: 580, height: 269, top: 0.15, right: 0.012),
      _buildBranchPicker(),
    );
    Overlay.of(context).insert(_overlayEntry!);
  }

  OverlayEntry _createOverlayEntry(
    BuildContext context,
    SizedItem size,
    Widget child,
  ) {
    return OverlayEntry(
      builder: (context) {
        return Stack(
          children: [
            Positioned.fill(
              child: GestureDetector(
                onTap: () {
                  _closeOverlay();
                },
              ),
            ),
            Positioned(
              right: MediaQuery.of(context).size.width - (size.width + 35),
              top:
                  MediaQuery.of(context).size.height * (size.top + size.height),
              width: size.width,
              height: size.height + 25,
              child: CompositedTransformFollower(
                link: consoleShellLayerLink,
                //  offset: Offset(-size.width + 155, 40),
                showWhenUnlinked: false,
                child: Material(
                  elevation: 8.0,
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: Palette.stroke),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: child,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _closeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  Widget _buildBranchPicker() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus(); // Request focus after the widget is built
    });
    searchData = null;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: TextField(
                focusNode: _focusNode,
                onChanged: (value) async {
                  if (value.isNotEmpty) {
                    _deBouncer.run(() async {
                      _loading.value = true;

                      searchData = null;
                      final res =
                          await ref.read(globalSearchUseCaseProvider(value));

                      res.when(success: (data) {
                        searchData = data;
                        _loading.value = false;
                      }, failure: (error, _) {
                        Toast.apiError(error, context);
                        _loading.value = false;
                      });
                    });
                  }
                },
                style: Theme.of(context).textTheme.bodyMedium,
                textAlignVertical: TextAlignVertical.center,
                cursorColor: Palette.strokePressed,
                cursorHeight: 14,
                decoration: InputDecoration(
                  hintText: 'Search',
                  prefixIcon: Padding(
                    padding: const EdgeInsets.only(top: 6.0),
                    child: SizedBox(
                      width: 16,
                      height: 16,
                      child: SvgPicture.asset(
                        kSearchSvg,
                        fit: BoxFit.scaleDown,
                      ),
                    ),
                  ),
                  constraints: BoxConstraints.tight(
                    const Size(double.maxFinite, 40),
                  ),
                  border: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                ),
              ),
            ),
            InkWell(
                onTap: () => _closeOverlay(),
                child: const Padding(
                    padding: EdgeInsets.all(8),
                    child: Icon(
                      Icons.clear,
                      size: 12,
                    )))
          ],
        ),
        Divider(
          color: Palette.kE7E7E7,
        ),
        ValueListenableBuilder<bool>(
          valueListenable: _loading,
          builder: (ctx, value, _) {
            return Skeletonizer(
              enabled: value,
              child: searchData == null
                  ? value
                      ? const Padding(
                          padding: EdgeInsets.all(8.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Column(
                                children: [
                                  //simulate loading state
                                  Text(
                                      '......................................'),
                                  Text('..................................'),
                                  Text('.............................'),
                                ],
                              ),
                            ],
                          ),
                        )
                      : Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Orders',
                                style: TextStyle(color: Colors.grey),
                              ),
                              const Gap(2),
                              Divider(
                                color: Palette.kE7E7E7,
                              ),
                              const Gap(15),
                              const Text(
                                'Draft Orders',
                                style: TextStyle(color: Colors.grey),
                              ),
                              const Gap(2),
                              Divider(
                                color: Palette.kE7E7E7,
                              ),
                              const Gap(15),
                              const Text(
                                'Invoices',
                                style: TextStyle(color: Colors.grey),
                              ),
                            ],
                          ),
                        )
                  : Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: SizedBox(
                        height: 200,
                        child: SingleChildScrollView(
                          child: Row(
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Orders',
                                    style: TextStyle(color: Colors.grey),
                                  ),
                                  const Gap(5),
                                  Column(
                                      children: searchData!.orders!
                                          .map(
                                            (e) => InkWell(
                                              onTap: () {
                                                _closeOverlay();
                                                ctx.pushNamed(
                                                  kOrderSummaryRoute,
                                                  pathParameters: {
                                                    'id': e.paymentReference!
                                                  },
                                                );
                                              },
                                              child: Padding(
                                                padding: const EdgeInsets.only(
                                                    bottom: 4),
                                                child: Text(
                                                  e.paymentReference ?? '',
                                                  style: const TextStyle(
                                                      fontWeight:
                                                          FontWeight.w500),
                                                ),
                                              ),
                                            ),
                                          )
                                          .toList()),
                                  const Gap(5),
                                  const Text(
                                    'Invoices',
                                    style: TextStyle(color: Colors.grey),
                                  ),
                                  const Gap(5),
                                  Column(
                                      children: searchData!.invoices!
                                          .map((e) => InkWell(
                                                onTap: () {
                                                  _closeOverlay();
                                                  showCustomGeneralDialog(
                                                    ctx,
                                                    dismissible: true,
                                                    percentage: 0.45,
                                                    child: InvoiceSummary(
                                                      e.invoiceNumber
                                                          .toString(),
                                                      isCloseIcon: true,
                                                      args: InvoiceSummaryArgs(
                                                          invoice: e),
                                                    ),
                                                  );
                                                },
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          bottom: 4),
                                                  child: Text(
                                                    e.invoiceNumber.toString(),
                                                    style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.w500),
                                                  ),
                                                ),
                                              ))
                                          .toList()),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
            );
          },
        )
      ],
    );
  }
}

class DeBouncer {
  final int milliseconds;
  Timer? _timer;

  DeBouncer({required this.milliseconds});

  void run(VoidCallback action) {
    _timer?.cancel();
    _timer = Timer(Duration(milliseconds: milliseconds), action);
  }
}

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_commons_flutter/models/currency.dart';
import 'package:td_commons_flutter/models/dispatched_user.dart';
import 'package:td_commons_flutter/models/driver.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/order/domain/entities/order_params.dart';
import 'package:td_procurement/app/order/presentation/controllers/options_provider.dart';
import 'package:td_procurement/app/order/presentation/controllers/order_controller.dart';
import 'package:td_procurement/app/order/presentation/controllers/order_state.dart';
import 'package:td_procurement/app/order/presentation/widgets/empty_orders.dart';
import 'package:td_procurement/app/order/presentation/widgets/order_assign_driver.dart';
import 'package:td_procurement/app/order/presentation/widgets/order_pagination.dart';
import 'package:td_procurement/app/order/presentation/widgets/orders_table.dart';
import 'package:td_procurement/app/order/presentation/widgets/sales_orders_table.dart';
import 'package:td_procurement/app/shipments/presentation/widgets/date_picker.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

class SalesOrdersScreen extends ConsumerStatefulWidget {
  const SalesOrdersScreen(this.refreshSalesOrders, {super.key});

  final bool refreshSalesOrders;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SalesOrdersScreenState();
}

class _SalesOrdersScreenState extends ConsumerState<SalesOrdersScreen> {
  final _searchController = TextEditingController();
  Timer? _searchDebounceTimer;
  int _activeStatusIndex = 0;
  String? _salesLocationId;

  @override
  void initState() {
    super.initState();
    _initializeState();
    _fetchInitialData();
  }

  void _initializeState() {
    final orderState = ref.read(orderControllerProvider);
    _activeStatusIndex = orderState.fetchSalesOrdersParams.activeIndex;
    _searchController.text = orderState.fetchSalesOrdersParams.searchText;

    final user = ref.read(userControllerProvider);
    _salesLocationId = user?.retailOutlets?.firstOrNull?.salesLocationId;
  }

  void _fetchInitialData() {
    _fetchSalesOrders();
    _fetchSalesLocationDrivers();
  }

  @override
  void didUpdateWidget(SalesOrdersScreen oldWidget) {
    if (widget.refreshSalesOrders != oldWidget.refreshSalesOrders) {
      _fetchInitialData();
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    _searchController.dispose();
    super.dispose();
  }

  void _fetchSalesOrders() {
    final params = ref.read(orderControllerProvider).fetchSalesOrdersParams;
    Future.microtask(() {
      ref
          .read(orderControllerProvider.notifier)
          .fetchSalesOrders(params, forced: widget.refreshSalesOrders);
    });
  }

  void _fetchSalesLocationDrivers() {
    if (_salesLocationId == null) return;

    final params =
        ref.read(orderControllerProvider).fetchSalesLocationDriversParams;
    final currentDrivers =
        ref.read(orderControllerProvider).salesLocationDrivers;

    final shouldFetch =
        currentDrivers is! AsyncData || widget.refreshSalesOrders;
    if (!shouldFetch) return;

    Future.microtask(() {
      ref.read(orderControllerProvider.notifier).fetchSalesLocationDrivers(
            params.copyWith(locationId: _salesLocationId),
            forced: widget.refreshSalesOrders,
          );
    });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final orderState = ref.watch(orderControllerProvider);
    final statusOptions = orderState.salesOrderStatusOptions;
    final drivers = orderState.salesLocationDrivers.value;
    final selectedOrders = ref.watch(optionsProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(textTheme),
        _buildStatusFilterBar(
            orderState, statusOptions, selectedOrders, drivers),
        _buildSearchAndOptionsBar(orderState),
        _buildOrdersContent(orderState, selectedOrders),
      ],
    );
  }

  Widget _buildHeader(TextTheme textTheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(20),
        Padding(
          padding: const EdgeInsets.only(left: 40),
          child: Text(
            'Sales Orders',
            style: textTheme.headlineMedium,
          ),
        ),
        const Gap(20),
      ],
    );
  }

  Widget _buildStatusFilterBar(
    OrderState orderState,
    List<String> statusOptions,
    List<Order> selectedOrders,
    List<Driver>? drivers,
  ) {
    return Container(
      padding: const EdgeInsets.only(left: 40, bottom: 12),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Palette.stroke)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            flex: 3,
            child: _buildStatusFilters(orderState, statusOptions),
          ),
          Flexible(
            flex: 2,
            child:
                _buildDateFilterAndOptions(orderState, selectedOrders, drivers),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusFilters(
      OrderState orderState, List<String> statusOptions) {
    return Row(
      children: statusOptions.map((status) {
        final index = statusOptions.indexOf(status);
        final isSelected = _activeStatusIndex == index;

        return Flexible(
          child: StatusFilteringWidget(
            status,
            isSelected,
            width: 130,
            padding: const EdgeInsets.symmetric(vertical: 19),
            onPressed: () => _handleStatusFilter(orderState, index),
          ),
        );
      }).toList(),
    );
  }

  void _handleStatusFilter(OrderState orderState, int index) {
    if (orderState.salesOrders.isLoading || index == _activeStatusIndex) return;

    setState(() => _activeStatusIndex = index);
    _updateOrdersForStatus(index);
  }

  void _updateOrdersForStatus(int index) {
    final status =
        ref.read(orderControllerProvider).salesOrderStatusOptions[index];

    final params = _getParamsForStatus(status);
    final orderNotifier = ref.read(orderControllerProvider.notifier);

    orderNotifier.setSalesOrdersDateFilter(null, null);
    orderNotifier.fetchSalesOrders(
      params.copyWith(activeIndex: index),
      forced: true,
    );
  }

  FetchSalesOrdersParams _getParamsForStatus(String status) {
    return switch (status.toLowerCase()) {
      'all orders' =>
        FetchSalesOrdersParams.defaultValue().copyWith(status: 'all'),
      'pending' =>
        FetchSalesOrdersParams.defaultValue().copyWith(status: 'open'),
      'completed' =>
        FetchSalesOrdersParams.defaultValue().copyWith(status: 'shipped'),
      _ => FetchSalesOrdersParams.defaultValue()
          .copyWith(status: status.toLowerCase()),
    };
  }

  Widget _buildDateFilterAndOptions(
    OrderState orderState,
    List<Order> selectedOrders,
    List<Driver>? drivers,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        DatePickerWidget(
          initialDate: orderState.salesOrdersStartDate,
          selectedStartDate: orderState.salesOrdersStartDate,
          selectedEndDate: orderState.salesOrdersEndDate,
          getValueLabel: () => getFormattedDateRange(
            orderState.salesOrdersStartDate,
            orderState.salesOrdersEndDate,
          ),
          onDatesSelected: _handleDateSelection,
          onCancel: _handleDateFilterClear,
        ),
        const Gap(20),
        if (selectedOrders.isNotEmpty)
          _buildAssignDriverButton(selectedOrders, drivers),
      ],
    );
  }

  Future<void> _handleDateSelection(
      {DateTime? startDate, DateTime? endDate}) async {
    startDate ??= endDate;
    endDate ??= startDate;

    final orderNotifier = ref.read(orderControllerProvider.notifier);
    final params = ref.read(orderControllerProvider).fetchSalesOrdersParams;

    orderNotifier.setSalesOrdersDateFilter(startDate, endDate);
    await Future.delayed(Duration.zero);

    orderNotifier.fetchSalesOrders(
      params.copyWith(selectedDates: [startDate, endDate], currentPage: 1),
      forced: true,
    );
  }

  Future<void> _handleDateFilterClear() async {
    final stateParams =
        ref.read(orderControllerProvider).fetchSalesOrdersParams;
    if (!stateParams.selectedDates.any((date) => date != null)) return;

    final params = FetchSalesOrdersParams.defaultValue().copyWith(
      status: stateParams.status,
      searchText: stateParams.searchText,
      activeIndex: stateParams.activeIndex,
    );

    final orderNotifier = ref.read(orderControllerProvider.notifier);
    orderNotifier.setSalesOrdersDateFilter(null, null);
    await Future.delayed(Duration.zero);

    orderNotifier.fetchSalesOrders(params, forced: true);
  }

  Widget _buildSearchAndOptionsBar(
    OrderState orderState,
    // List<Order> selectedOrders,
    // List<Driver>? drivers,
  ) {
    return Row(
      children: [
        Expanded(
          child: _buildSearchBar(orderState),
        ),
        // if (selectedOrders.isNotEmpty)
        //   _buildAssignDriverButton(selectedOrders, drivers),
      ],
    );
  }

  Widget _buildSearchBar(OrderState orderState) {
    return Container(
      height: 48,
      padding: const EdgeInsets.only(left: 40),
      child: TextFormField(
        controller: _searchController,
        decoration: _buildSearchInputDecoration(),
        cursorColor: Palette.strokePressed,
        cursorHeight: 18,
        onChanged: _handleSearchInput,
      ),
    );
  }

  InputDecoration _buildSearchInputDecoration() {
    return InputDecoration(
      hintText: 'Type to search by destination',
      prefixIcon: Padding(
        padding: const EdgeInsets.only(top: 2.0, right: 10),
        child: Skeleton.replace(
          child: SvgPicture.asset(
            '$kSvgDir/order/search.svg',
            fit: BoxFit.cover,
          ),
        ),
      ),
      prefixIconConstraints: const BoxConstraints(
        minWidth: 22,
        minHeight: 22,
      ),
      hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Palette.placeholder,
            fontWeight: FontWeight.w400,
          ),
      border: InputBorder.none,
      enabledBorder: InputBorder.none,
      focusedBorder: InputBorder.none,
      errorBorder: InputBorder.none,
      disabledBorder: InputBorder.none,
    );
  }

  void _handleSearchInput(String query) {
    _searchDebounceTimer?.cancel();
    _searchDebounceTimer = Timer(
      const Duration(milliseconds: 500),
      () => _performSearch(query.trim()),
    );
  }

  void _performSearch(String query) {
    final params = ref.read(orderControllerProvider).fetchSalesOrdersParams;
    ref.read(orderControllerProvider.notifier).fetchSalesOrders(
          params.copyWith(searchText: query, currentPage: 1),
          forced: true,
        );
  }

  Widget _buildAssignDriverButton(
      List<Order> selectedOrders, List<Driver>? drivers) {
    return Flexible(
      flex: 1,
      child: Padding(
        padding: const EdgeInsets.only(left: 115.0, right: 40.0),
        child: Container(
          width: 121,
          height: 41,
          decoration: BoxDecoration(
            border: Border.all(color: Palette.stroke),
            borderRadius: BorderRadius.circular(8),
          ),
          child: _buildDriverDropdown(selectedOrders, drivers),
        ),
      ),
    );
  }

  Widget _buildDriverDropdown(
      List<Order> selectedOrders, List<Driver>? drivers) {
    return DropdownButtonWidget(
      key: UniqueKey(),
      onChanged: (value) {
        if (value?.toLowerCase() == 'assign to driver') {
          _showAssignDriverDialog(selectedOrders, drivers);
        }
      },
      itemToString: (item) => item,
      title: 'Options',
      hint: 'Options',
      isExpanded: true,
      menuMaxHeight: 300,
      options: const ["Assign to driver"],
    );
  }

  void _showAssignDriverDialog(
      List<Order> selectedOrders, List<Driver>? drivers) {
    showCustomGeneralDialog(
      context,
      percentage: 0.6,
      dismissible: false,
      child: OrderAssignDriverWidget(selectedOrders, drivers),
    );
  }

  Widget _buildOrdersContent(
      OrderState orderState, List<Order> selectedOrders) {
    return Expanded(
      child: orderState.salesOrders.when(
        data: (orders) => orders.isEmpty
            ? const EmptyOrdersWidget(OrderType.sales)
            : _buildOrdersList(orders, selectedOrders),
        loading: () => _buildLoadingState(selectedOrders),
        error: (e, s) => _buildErrorState(e),
      ),
    );
  }

  Widget _buildOrdersList(List<Order> orders, List<Order> selectedOrders) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_shouldShowTableHeader(orders))
          Skeletonizer(
            enabled: false,
            child: _buildTableHeader(),
          ),
        Expanded(child: SalesOrdersTableWidget(orders, selectedOrders)),
        const Gap(10),
        const SalesOrderPagination(),
        const Gap(10),
      ],
    );
  }

  bool _shouldShowTableHeader(List<Order> orders) {
    final orderState = ref.read(orderControllerProvider);
    return orderState.salesOrders.isLoading || orders.isNotEmpty;
  }

  Widget _buildLoadingState(List<Order> selectedOrders) {
    return Skeletonizer(
      enabled: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: SalesOrdersTableWidget(
              _generatePlaceholderOrders(),
              selectedOrders,
            ),
          ),
          const SalesOrderPagination(),
          const Gap(20),
        ],
      ),
    );
  }

  List<Order> _generatePlaceholderOrders() {
    return List.filled(
      10,
      Order(
        id: 'id',
        total: 10000,
        summary: 'summary',
        customerName: 'customerName',
        createdAt: DateTime.now(),
        status: 'status',
        currency: Currency(iso: 'iso', symbol: 'symbol'),
        dispatchUser: DispatchUser(
          name: 'assigned',
          phoneNumber: 'phoneNumber',
        ),
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    if (kIsWeb || kIsWasm) {
      return _buildWebErrorFallback();
    }

    return FailureWidget(
      fullScreen: true,
      heightFactor: 0.7,
      e: error,
      retry: () {
        final params = ref.read(orderControllerProvider).fetchSalesOrdersParams;
        ref.read(orderControllerProvider.notifier).fetchSalesOrders(params);
      },
    );
  }

  Widget _buildWebErrorFallback() {
    return Skeletonizer(
      enabled: true,
      child: SalesOrdersTableWidget(
        List.filled(
          20,
          Order(
            id: 'id',
            total: 10000,
            summary: 'summary',
            customerName: 'customerName',
            createdAt: DateTime.now(),
            status: 'status',
            currency: Currency(iso: 'iso', symbol: 'symbol'),
          ),
        ),
        const [],
      ),
    );
  }

  Widget _buildTableHeader() {
    final textTheme = Theme.of(context).textTheme;
    final orders = ref.watch(orderControllerProvider).salesOrders.value;
    final unassignedOrdersCount = _getUnassignedOrdersCount(orders);

    return buildTableHeader(
      tableHeader: Table(
        columnWidths: const {
          0: FlexColumnWidth(0.35),
          1: FlexColumnWidth(1.4),
          2: FlexColumnWidth(1.6),
          3: FlexColumnWidth(1.4),
          4: FlexColumnWidth(1.4),
          5: FlexColumnWidth(1.9),
          6: FlexColumnWidth(2),
          7: FlexColumnWidth(0.5),
        },
        children: [
          TableRow(
            children: [
              _buildSelectAllCheckbox(unassignedOrdersCount, orders),
              buildHeaderCell('Amount', textTheme),
              buildHeaderCell('Summary', textTheme),
              buildHeaderCell('Delivering to', textTheme),
              buildHeaderCell('Created on', textTheme),
              buildHeaderCell('Assigned to', textTheme),
              Container(), // Empty space for floating icon
            ],
          ),
        ],
      ),
    );
  }

  int _getUnassignedOrdersCount(List<Order>? orders) {
    return orders
            ?.where((order) => ['pending', 'skipped']
                .contains(order.shippingStatus?.toLowerCase()))
            .length ??
        0;
  }

  Widget _buildSelectAllCheckbox(
      int unassignedOrdersCount, List<Order>? orders) {
    if (unassignedOrdersCount == 0) return Container();

    return Skeletonizer(
      enabled: ref.watch(orderControllerProvider).salesOrders.isLoading,
      child: Skeleton.shade(
        child: Checkbox(
          value: ref.read(optionsProvider).length == unassignedOrdersCount,
          onChanged: (value) => _handleSelectAll(value, orders),
        ),
      ),
    );
  }

  void _handleSelectAll(bool? value, List<Order>? orders) {
    if (value == true) {
      ref.read(optionsProvider.notifier).selectAllItems(orders ?? []);
    } else {
      ref.read(optionsProvider.notifier).clearList();
    }
  }
}

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_procurement/app/order/presentation/controllers/order_controller.dart';
import 'package:td_procurement/app/order/presentation/controllers/order_state.dart';
import 'package:td_procurement/app/order/presentation/widgets/order_acceptance.dart';
import 'package:td_procurement/app/order/presentation/widgets/order_action_bar.dart';
import 'package:td_procurement/app/order/presentation/widgets/order_summary_table.dart';
import 'package:td_procurement/app/order/presentation/widgets/order_tracking.dart';
import 'package:td_procurement/app/order/presentation/widgets/printer.dart';
import 'package:td_procurement/core/models/index.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';
import 'package:td_procurement/src/utils/order_utils.dart';

class OrderSummaryScreen extends ConsumerStatefulWidget {
  const OrderSummaryScreen(
    this.orderNumber, {
    super.key,
    this.transaction,
    this.action,
  });

  final String orderNumber;
  final Transaction? transaction;
  final String? action;

  @override
  ConsumerState<OrderSummaryScreen> createState() => OrderSummaryScreenState();
}

class OrderSummaryScreenState extends ConsumerState<OrderSummaryScreen> {
  late final String orderNumber = widget.orderNumber;
  late final bool isOrderAcceptanceAction =
      widget.action?.toLowerCase() == 'accept';
  late bool isDraftOrder;
  Transaction? transaction;

  final ValueNotifier<bool> rightButton1 = ValueNotifier<bool>(false);
  final ValueNotifier<bool> rightButton2 = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    _initializeTransaction();
  }

  /// Initializes the transaction either from the widget parameter,
  /// the current transaction in view, or fetches it using the order number
  void _initializeTransaction() {
    final orderController = ref.read(orderControllerProvider.notifier);
    transaction = widget.transaction ??
        ref.read(orderControllerProvider).transactionInView ??
        orderController.transaction(widget.orderNumber);

    isDraftOrder = transaction?.isDraft ?? false;

    // Only fetch order details for non-draft orders
    if (!isDraftOrder) {
      Future.microtask(fetchOrderDetails);
    }
  }

  void fetchOrderDetails() {
    ref.read(orderControllerProvider.notifier).fetchOrderDetails(orderNumber);
  }

  @override
  Widget build(BuildContext context) {
    _setupOrderControllerListener();
    final orderDetails = ref.watch(orderControllerProvider).orderDetails;

    return Scaffold(
      body: Column(
        children: [
          _buildActionBar(orderDetails),
          Expanded(
            child: SingleChildScrollView(
              child: isDraftOrder
                  ? _buildDraftOrderContent()
                  : _buildOrderContent(orderDetails),
            ),
          ),
        ],
      ),
    );
  }

  void _setupOrderControllerListener() {
    ref.listen(orderControllerProvider, (oldState, newState) {
      _handleOrderDetailsUpdate(newState);
      _handleErrors(newState, context);
    });
  }

  void _handleOrderDetailsUpdate(OrderState newState) {
    if (!newState.orderDetails.isLoading &&
        !newState.orderDetails.hasError &&
        newState.orderDetails.hasValue) {
      final newTransaction = newState.orderDetails.value?.transaction;
      final isSameTransaction =
          transaction == null ? true : transaction?.id == newTransaction?.id;

      if (newTransaction != null && isSameTransaction) {
        setState(() {
          transaction = newTransaction;
          // Only update isDraftOrder if it hasn't been set yet
          if (!isDraftOrder) {
            isDraftOrder = newTransaction.isDraft;
          }
        });
      }
    }
  }

  void _handleErrors(OrderState newState, BuildContext context) {
    if (newState.showOrderDetailsError!) {
      Toast.error(
        'Cannot complete action, please try again later!',
        context,
        title: 'Error fetching order details',
      );
      ref.read(orderControllerProvider.notifier).hideOrderDetailsError();
    }
  }

  Widget _buildActionBar(AsyncValue<OrderDetails?> orderDetails) {
    return MultiValueListenableBuilder<bool, bool, Null>(
      valueListenable1: rightButton1,
      valueListenable2: rightButton2,
      builder: (context, loading1, loading2, _, __) {
        return OrderActionBarWidget(
          leftText: 'Purchase Order Summary',
          isCloseIcon: false,
          leftIconAction: () => context.pop(),
          rightButton1Text: 'Print',
          rightButton1Loading: loading1,
          rightButton1Action: () =>
              handlePrintOrderAction(orderDetails.value?.orders),
          rightButton2Text: isDraftOrder ? 'Edit' : null,
          rightButton2Action: isDraftOrder
              ? () => context.pushNamed(kCreateOrderRoute, extra: transaction)
              : null,
        );
      },
    );
  }

  Widget _buildDraftOrderContent() {
    return const Padding(
      padding: EdgeInsets.symmetric(horizontal: 40),
      child: OrderSummaryTableWidget(),
    );
  }

  Widget _buildOrderContent(AsyncValue<OrderDetails?> orderDetails) {
    return orderDetails.when(
      skipLoadingOnReload: true,
      data: (data) => _buildOrderDetailsContent(data),
      loading: () => loadingWidget(),
      error: (e, s) => _buildErrorWidget(e),
    );
  }

  Widget _buildOrderDetailsContent(OrderDetails? data) {
    return Column(
      children: [
        AnimatedSwitcherWidget(
          child: isOrderAcceptanceAction && data?.shipment == null
              ? OrderAcceptanceWidget(
                  widget.orderNumber,
                  onSuccess: fetchOrderDetails,
                )
              : const SizedBox.shrink(),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 40),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 5,
                child: OrderSummaryTableWidget(
                  orderDetails: data,
                  transaction: transaction,
                ),
              ),
              const Gap(10),
              Expanded(
                flex: 3,
                child: Padding(
                  padding: const EdgeInsets.only(top: 30),
                  child: OrderTrackingWidget(data),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildErrorWidget(Object e) {
    if (kIsWeb || kIsWasm) return loadingWidget();
    return FailureWidget(
      fullScreen: true,
      heightFactor: 0.7,
      e: e,
      retry: () => ref
          .read(orderControllerProvider.notifier)
          .fetchOrderDetails(orderNumber),
    );
  }

  Widget loadingWidget() {
    return Skeletonizer(
      enabled: true,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 40),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 5,
              child: OrderSummaryTableWidget(
                orderDetails: OrderDetails.defaultValue(),
              ),
            ),
            const Gap(10),
            Expanded(
              flex: 3,
              child: Padding(
                padding: const EdgeInsets.only(top: 30),
                child: OrderTrackingWidget(
                  OrderDetails.defaultValue(),
                  isLoading: true,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> handlePrintOrderAction(List<Order>? orders) async {
    if (transaction == null) return;

    rightButton1.value = true;

    final printOrder = await processPrintOrder(transaction!, ref, orders);
    if (printOrder == null) {
      if (mounted) {
        rightButton1.value = false;
        Toast.error('Error printing order', context, title: 'Print Error');
      }
      return;
    }

    final branches = ref.read(branchesProvider);
    final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;
    final branch = branches.firstWhere(
      (x) => x.id == transaction!.retailOutletId,
      orElse: () => branches.first,
    );

    final printer =
        OrderPrinter(printOrder, branch, isExportCountry, 'Purchase Order');
    final pdfData = await printer.generatePdf();

    rightButton1.value = false;
    await printer.printPdf(pdfData);
  }
}

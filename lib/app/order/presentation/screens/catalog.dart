import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/order/presentation/controllers/cart_notifier.dart';
import 'package:td_procurement/app/order/presentation/controllers/order_controller.dart';
import 'package:td_procurement/app/order/presentation/controllers/order_state.dart';
import 'package:td_procurement/app/order/presentation/widgets/catalog_table.dart';
import 'package:td_procurement/app/order/presentation/widgets/catalog_view_switcher.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class CatalogScreen extends ConsumerStatefulWidget {
  const CatalogScreen(this.refreshCatalog, {super.key});

  final bool refreshCatalog;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _OrderScreenState();
}

class _OrderScreenState extends ConsumerState<CatalogScreen> {
  Timer? _debounce;
  int activeIndex = 0;
  String? hexCode;

  late final isExportCountry =
      ref.read(countryTypeProvider) == CountryType.export;

  @override
  void initState() {
    super.initState();
    activeIndex = 0;
    hexCode = ref.read(hexCodeProvider);

    Future.microtask(() {
      // reset states
      ref.read(catalogBrandFilterProvider.notifier).state = '';
      ref.read(catalogCategoryFilterProvider.notifier).state = 'All';
      ref.read(catalogPriceFilterProvider.notifier).state = null;

      // fetch catalog data
      ref.read(countryTypeProvider).when(
        export: () {
          ref.read(orderControllerProvider.notifier).fetchExportVariants();
        },
        nonExport: () {
          if (hexCode != null) {
            _fetchNonExportVariants();
          }
        },
      );
    });
  }

  @override
  void dispose() {
    _debounce?.cancel();
    super.dispose();
  }

  void _fetchNonExportVariants() {
    ref.read(orderControllerProvider.notifier).fetchCollections(hexCode!);
    ref.read(orderControllerProvider.notifier).fetchOutletVariants();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    final orderState = ref.watch(orderControllerProvider);
    final orderNotifier = ref.read(orderControllerProvider.notifier);
    // final cartState = ref.watch(cartProvider);

    final catalogCategoryFilter =
        orderNotifier.exclusivesCategoriesFilter; // categoriesFilter
    final catalogBrandFilter =
        orderNotifier.exclusivesBrandsFilter; // brandsFilter

    final catalogLoading = isExportCountry
        ? orderState.exportVariants.isLoading
        : orderState.exclusiveCollections.isLoading;

    return Column(
      children: [
        Skeletonizer(
          enabled: catalogLoading,
          child: Container(
            decoration: BoxDecoration(
                border: Border(
                    bottom: BorderSide(
              color: Palette.stroke,
            ))),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Gap(20), // 40
                Skeleton.keep(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 40),
                    child: Text(
                      'Catalog',
                      style: textTheme.headlineMedium,
                    ),
                  ),
                ),
                const Gap(20),
                Container(
                  height: 60,
                  width: double.maxFinite,
                  padding: const EdgeInsets.only(left: 40).copyWith(bottom: 12),
                  child: SingleChildScrollView(
                    // key: UniqueKey(),
                    physics: const BouncingScrollPhysics(),
                    scrollDirection: Axis.horizontal,
                    child: _buildCategoryOptions(),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Gap(40),
                    Expanded(
                      flex: 1,
                      child: _buildSearchBar(orderState),
                    ),
                    Gap(22.w),
                    Expanded(
                      flex: 1,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Flexible(
                            child: Container(
                              height: 36,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                boxShadow: const [
                                  BoxShadow(
                                    color: Palette.k0000000A,
                                    spreadRadius: -1,
                                    blurRadius: 2,
                                    offset: Offset(0, 2),
                                  )
                                ],
                                border: Border.all(color: Palette.kE7E7E7),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 8),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Flexible(
                                    // flex: 1,
                                    child: DropdownButtonWidget<
                                        CatalogPriceFilter>(
                                      key: UniqueKey(),
                                      isExpanded: true,
                                      // selectedValue: 'Price: High to Low',
                                      title: 'Price',
                                      hint: 'Price',
                                      onChanged:
                                          (CatalogPriceFilter? newValue) {
                                        ref
                                            .read(catalogPriceFilterProvider
                                                .notifier)
                                            .state = newValue;
                                      },
                                      options: const <CatalogPriceFilter>[
                                        CatalogPriceFilter.highToLow,
                                        CatalogPriceFilter.lowToHigh
                                      ],
                                      itemToString: (item) => item.name,
                                    ),
                                  ),
                                  VerticalDivider(
                                    color: Palette.kE7E7E7,
                                    thickness: 1,
                                  ),
                                  Flexible(
                                    // flex: 1,
                                    child: ref.read(countryTypeProvider).when(
                                      // To fix a UI discrepancy
                                      export: () {
                                        return DropdownButtonWidget<String>(
                                          isExpanded: true,
                                          selectedValue: ref.watch(
                                              catalogCategoryFilterProvider),
                                          title: 'Category',
                                          hint: 'Category',
                                          onChanged: (String? newValue) {
                                            final index = catalogCategoryFilter
                                                .indexOf(newValue!);
                                            setState(() {
                                              activeIndex = index;
                                              handleOrderOption(index);
                                            });
                                          },
                                          options: catalogCategoryFilter,
                                          itemToString: (item) => item,
                                        );
                                      },
                                      nonExport: () {
                                        return DropdownButtonWidget<String>(
                                          isExpanded: true,
                                          selectedValue: catalogLoading
                                              ? 'All'
                                              : (ref.watch(
                                                      catalogCategoryFilterProvider) ??
                                                  'All'),
                                          title: 'Category',
                                          hint: 'Category',
                                          onChanged: (String? newValue) {
                                            if (newValue != null) {
                                              final index =
                                                  catalogCategoryFilter
                                                      .indexOf(newValue);
                                              setState(() {
                                                activeIndex = index;
                                                handleOrderOption(index);
                                              });
                                            }
                                          },
                                          options: catalogLoading
                                              ? ['All']
                                              : ({
                                                  'All',
                                                  ...catalogCategoryFilter
                                                }.toList()), // Ensures 'All' is present and unique
                                          itemToString: (item) => item,
                                        );
                                      },
                                    ),
                                  ),
                                  VerticalDivider(
                                    color: Palette.kE7E7E7,
                                    thickness: 1,
                                  ),
                                  Flexible(
                                    // flex: 1,
                                    child: DropdownButtonWidget<String>(
                                      key: UniqueKey(),
                                      isExpanded: true,
                                      // selectedValue: 'Filter by',
                                      title: 'Brands',
                                      hint: 'Brands',
                                      onChanged: (String? newValue) {
                                        ref
                                            .read(catalogBrandFilterProvider
                                                .notifier)
                                            .state = newValue!;
                                      },
                                      options: catalogBrandFilter,
                                      itemToString: (item) => item,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const Gap(8),
                          Skeleton.shade(
                            child: CatalogViewSwitcher(
                              initialCatalogView: ref.read(catalogViewProvider),
                            ),
                          ),
                          const Gap(8),
                          // const Skeleton.shade(
                          //   child: CartBadgeWidget(),
                          // ),
                          const Gap(30),
                        ],
                      ),
                    ),
                  ],
                ),
                const Gap(20),
              ],
            ),
          ),
        ),
        Expanded(
          child: ref.read(countryTypeProvider).when(
            export: () {
              return orderState.exportVariants.when(
                data: (data) {
                  if (data.isEmpty) {
                    return const EmptyWidget(
                      icon: '$kSvgDir/order/cart.svg',
                      title: 'No items for your location',
                      subTitle:
                          'We’re expanding quickly so please check back soon.',
                      baseline: false,
                    );
                  }
                  return CatalogTableWidget(data, catalogLoading);
                },
                loading: () {
                  return Skeletonizer(
                    enabled: true,
                    child: CatalogTableWidget(
                        List.filled(
                            10, Variant(variantId: '', name: '', category: '')),
                        catalogLoading),
                  );
                },
                error: (e, s) {
                  return FailureWidget(
                    fullScreen: true,
                    heightFactor: 0.7,
                    e: e,
                    retry: () => ref
                        .read(orderControllerProvider.notifier)
                        .fetchExportVariants(),
                  );
                },
              );
            },
            nonExport: () {
              // return orderNotifier.nonExportVariants.when(
              return orderNotifier.exclusiveVariants.when(
                data: (data) {
                  if (data.isEmpty) {
                    return const EmptyWidget(
                      icon: '$kSvgDir/order/cart.svg',
                      title: 'No items for your location',
                      subTitle:
                          'We’re expanding quickly so please check back soon.',
                      baseline: false,
                    );
                  }
                  return CatalogTableWidget(data, catalogLoading);
                },
                loading: () {
                  return Skeletonizer(
                    enabled: true,
                    child: CatalogTableWidget(
                      List.filled(
                          10, Variant(variantId: '', name: '', category: '')),
                      catalogLoading,
                    ),
                  );
                },
                error: (e, s) {
                  return FailureWidget(
                    fullScreen: true,
                    heightFactor: 0.7,
                    e: e,
                    retry: _fetchNonExportVariants,
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryOptions() {
    return Consumer(
      builder: (context, ref, child) {
        ref.watch(cartProvider);
        final orderState = ref.read(orderControllerProvider);
        final orderNotifier = ref.read(orderControllerProvider.notifier);

        final catalogCategoryOptions = orderNotifier.exclusivesCategoriesFilter;
        return Row(
          children: catalogCategoryOptions.map(
            (status) {
              int index = catalogCategoryOptions.indexOf(status);
              bool isSelected = orderState.exportVariants.isLoading
                  ? false
                  : activeIndex == index;

              return StatusFilteringWidget(
                status,
                isSelected,
                width: 110,
                height: 35,
                onPressed: () {
                  if (!orderState.exportVariants.isLoading &&
                      index != activeIndex) {
                    setState(() {
                      activeIndex = index;
                    });
                    handleOrderOption(index);
                  }
                },
              );
            },
          ).toList(),
        );
      },
    );
  }

  Widget _buildSearchBar(OrderState orderState) {
    final textTheme = Theme.of(context).textTheme;
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
          child: Container(
            height: 36,
            padding: const EdgeInsets.only(left: 20),
            decoration: BoxDecoration(
              border: Border.all(color: Palette.stroke),
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextFormField(
              initialValue: ref.watch(catalogSearchProvider),
              decoration: InputDecoration(
                hintText: 'Search',
                prefixIcon: Padding(
                  padding: const EdgeInsets.only(top: 2.0, right: 10),
                  child: Skeleton.shade(
                    child: SvgPicture.asset(
                      '$kSvgDir/order/search.svg',
                      fit: BoxFit.contain,
                      width: 16,
                      height: 16,
                    ),
                  ),
                ),
                prefixIconConstraints: const BoxConstraints(
                  minWidth: 22,
                  minHeight: 22,
                ),
                hintStyle: textTheme.bodyMedium?.copyWith(
                  color: Palette.placeholder,
                  fontWeight: FontWeight.w400,
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 15.5),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
              ),
              cursorColor: Palette.strokePressed,
              cursorHeight: 14,
              onChanged: _onTextChanged,
            ),
          ),
        ),
      ],
    );
  }

  handleOrderOption(int index) {
    final status = ref
        .read(orderControllerProvider.notifier)
        .exclusivesCategoriesFilter[index];
    ref.read(catalogBrandFilterProvider.notifier).state = '';
    ref.read(catalogPriceFilterProvider.notifier).state = null;
    ref.read(catalogCategoryFilterProvider.notifier).state = status;
  }

  void _onTextChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 1), () {
      ref.read(catalogSearchProvider.notifier).state = query;
    });
  }
}

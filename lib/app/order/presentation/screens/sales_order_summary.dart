import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_commons_flutter/models/retail_branch.dart';
import 'package:td_procurement/app/order/domain/use_cases/order_use_cases.dart';
import 'package:td_procurement/app/order/presentation/widgets/order_action_bar.dart';
import 'package:td_procurement/app/order/presentation/widgets/order_delivery.dart';
import 'package:td_procurement/app/order/presentation/widgets/order_summary_table.dart';
import 'package:td_procurement/app/order/presentation/widgets/order_tracking.dart';
import 'package:td_procurement/app/order/presentation/widgets/printer.dart';
import 'package:td_procurement/app/order/presentation/widgets/sales_order_summary_table.dart';
import 'package:td_procurement/app/order/presentation/widgets/sales_order_tracking.dart';
import 'package:td_procurement/core/models/index.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/multi_value_listenable_builder.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';
import 'package:td_procurement/src/utils/order_utils.dart';

class SalesOrderSummaryScreen extends ConsumerStatefulWidget {
  const SalesOrderSummaryScreen(this.orderId, {super.key, this.order});

  final String orderId;
  final Order? order;

  @override
  ConsumerState<SalesOrderSummaryScreen> createState() =>
      _SalesOrderSummaryState();
}

class _SalesOrderSummaryState extends ConsumerState<SalesOrderSummaryScreen> {
  late final String orderId = widget.orderId;
  AsyncValue<Order> orderData = const AsyncValue.loading();

  final ValueNotifier<bool> rightButton1 = ValueNotifier<bool>(false);
  final ValueNotifier<bool> rightButton2 = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    _loadOrderData();
  }

  void _loadOrderData() {
    Future.microtask(prepOrderData);
  }

  Future<void> prepOrderData() async {
    if (!orderData.isLoading) {
      setState(() => orderData = const AsyncValue.loading());
    }

    final res = await ref.read(getSalesOrderUseCaseProvider(orderId));

    res.when(
      success: (data) => setState(() => orderData = AsyncValue.data(data)),
      failure: (error, _) {
        setState(() => orderData = AsyncValue.error(error, StackTrace.current));

        if (mounted) {
          Toast.error(
              'Cannot complete action, please try again later!', context,
              title: 'Error fetching sales order');
        }
      },
    );
  }

  bool _canMarkAsDelivered(AsyncValue<Order> data) {
    if (!data.hasValue || data.isLoading) return false;

    final order = data.value!;
    final shippingStatus = order.shippingStatus?.toLowerCase();
    final paymentStatus = order.paymentStatus?.toLowerCase();

    return isPendingOrSkipped(shippingStatus) && paymentStatus != 'paid';
  }

  void _handleMarkAsDelivered() {
    showCustomGeneralDialog(
      context,
      percentage: 0.4,
      dismissible: false,
      child: OrderDeliveryWidget(
        orderData.value!,
        onSuccess: () {
          prepOrderData();
          Toast.success('Order has been shipped', context);
        },
      ),
    );
  }

  void _handlePrint() {
    if (orderData.hasValue) {
      handlePrintOrderAction(orderData.value);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Skeletonizer(
        enabled: orderData.isLoading,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildActionBar(),
            Expanded(
              child: SingleChildScrollView(
                child: orderData.when(
                  skipLoadingOnReload: true,
                  data: _buildOrderContent,
                  loading: () => loadingWidget(),
                  error: (e, s) {
                    if (kIsWeb || kIsWasm) return loadingWidget();
                    return FailureWidget(
                      fullScreen: true,
                      heightFactor: 0.7,
                      e: e,
                      retry: prepOrderData,
                    );
                  },
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildActionBar() {
    return MultiValueListenableBuilder<bool, bool, Null>(
      valueListenable1: rightButton1,
      valueListenable2: rightButton2,
      builder: (context, loading1, loading2, _, __) {
        return OrderActionBarWidget(
          leftText: 'Sales Order Summary',
          isCloseIcon: false,
          leftIconAction: () => context.pop(),
          rightButton1Text: 'Print',
          rightButton1Loading: loading1,
          rightButton2Text:
              _canMarkAsDelivered(orderData) ? 'Mark as delivered' : null,
          rightButton2Action:
              _canMarkAsDelivered(orderData) ? _handleMarkAsDelivered : null,
          rightButton1Action: _handlePrint,
        );
      },
    );
  }

  Widget _buildOrderContent(Order data) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 40),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 5,
            child: SalesOrderSummaryTableWidget(data),
          ),
          const Gap(10),
          Expanded(
            flex: 3,
            child: Padding(
              padding: const EdgeInsets.only(top: 30),
              child: SalesOrderTrackingWidget(data),
            ),
          ),
        ],
      ),
    );
  }

  Widget loadingWidget() {
    return Skeletonizer(
      enabled: true,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 40),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 5,
              child: OrderSummaryTableWidget(
                  orderDetails: OrderDetails.defaultValue()),
            ),
            const Gap(10),
            Expanded(
              flex: 3,
              child: Padding(
                padding: const EdgeInsets.only(top: 30),
                child: OrderTrackingWidget(
                  OrderDetails.defaultValue(),
                  isLoading: true,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> handlePrintOrderAction(Order? order) async {
    if (order == null) return;

    rightButton1.value = true;

    final printOrder = processSalesPrintOrder(ref, order);
    if (printOrder == null) {
      if (mounted) {
        rightButton1.value = false;
        Toast.error('Error printing order', context, title: 'Print Error');
      }
      return;
    }

    final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;
    final branch = RetailBranch(
      id: order.retailOutletId ?? order.retailerId ?? '',
      outletBusinessName: order.customerName ?? '',
      contactPhone: order.contactPhone,
    );

    final printer =
        OrderPrinter(printOrder, branch, isExportCountry, 'Sales Order');
    final pdfData = await printer.generatePdf();

    rightButton1.value = false;
    await printer.printPdf(pdfData);
  }

  @override
  void dispose() {
    rightButton1.dispose();
    rightButton2.dispose();
    super.dispose();
  }
}

bool isDispatchedOrDelivered(String? shippingStatus) {
  if (shippingStatus == null) return false;
  return shippingStatus == 'dispatched' || shippingStatus == 'delivered';
}

bool isPendingOrSkipped(String? shippingStatus) {
  if (shippingStatus == null) return false;
  return shippingStatus == 'pending' || shippingStatus == 'skipped';
}

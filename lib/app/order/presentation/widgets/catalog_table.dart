import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/cart_item.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/order/domain/entities/order_params.dart';
import 'package:td_procurement/app/order/presentation/controllers/cart_notifier.dart';
import 'package:td_procurement/app/order/presentation/controllers/order_controller.dart';
import 'package:td_procurement/app/order/presentation/widgets/show_more_products.dart';
import 'package:td_procurement/app/order/presentation/widgets/variant_category_item.dart';
import 'package:td_procurement/app/order/presentation/widgets/variant_details.dart';
import 'package:td_procurement/app/order/presentation/widgets/variant_price.dart';
import 'package:td_procurement/app/order/presentation/widgets/variant_quantity_picker.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';
import 'package:td_procurement/src/utils/order_utils.dart';

final catalogViewProvider =
    StateProvider<CatalogViewType>((_) => CatalogViewType.grid);
final catalogSearchProvider = StateProvider<String>((_) => '');
final catalogCategoryFilterProvider = StateProvider<String>((_) => 'All');
final catalogBrandFilterProvider = StateProvider<String>((_) => '');
final catalogPriceFilterProvider =
    StateProvider<CatalogPriceFilter?>((_) => null);

enum CatalogPriceFilter {
  lowToHigh('Low to High'),
  highToLow('High to Low');

  final String name;

  const CatalogPriceFilter(this.name);

  @override
  String toString() => name;
}

class CatalogTableWidget extends ConsumerWidget {
  const CatalogTableWidget(this.variants, this.loading, {super.key});

  final List<Variant> variants;
  final bool loading;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final viewType = ref.watch(catalogViewProvider);
    ref.watch(catalogSearchProvider);
    ref.watch(catalogCategoryFilterProvider);
    ref.watch(catalogPriceFilterProvider);
    ref.watch(catalogBrandFilterProvider);

    final filteredVariants = _filteredVariants(variants, ref);
    // final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;

    if (filteredVariants.isEmpty) {
      return const CustomScrollView(
        slivers: [
          SliverFillRemaining(
            hasScrollBody: false,
            child: EmptyWidget(
              icon: '$kSvgDir/order/cart.svg',
              title: 'No products',
              subTitle: 'There are no products matching your filter criteria',
            ),
          ),
        ],
      );
    }

    return AnimatedSwitcherWidget(
      child: Consumer(
        builder: (context, ref, child) {
          // final showingOtherProducts =
          //     ref.watch(cartProvider).showingOtherProducts;
          return Column(
            children: [
              Expanded(
                child: viewType == CatalogViewType.grid
                    ? _buildItemsGrid(filteredVariants, loading)
                    : _buildTableView(context, filteredVariants),
              ),
              // if (!showingOtherProducts)
              //   Column(
              //     mainAxisAlignment: MainAxisAlignment.center,
              //     children: [
              //       const Gap(8),
              //       TextButton(
              //         onPressed: () => ref
              //             .read(cartProvider.notifier)
              //             .setShowingOtherProducts(true),
              //         style: ButtonStyle(
              //           shape: WidgetStateProperty.all<OutlinedBorder>(
              //               const RoundedRectangleBorder(
              //             borderRadius: BorderRadius.all(
              //               Radius.circular(30.0),
              //             ),
              //           )),
              //         ),
              //         child: const Text('See More'),
              //       ),
              //       const Gap(8),
              //     ],
              //   ),
            ],
          );
        },
      ),
    );
  }

  List<Variant> _filteredVariants(List<Variant> variants, WidgetRef ref) {
    final showingOtherProducts = ref.read(cartProvider).showingOtherProducts;

    if (showingOtherProducts) {
      final nonExportVariants =
          ref.read(orderControllerProvider.notifier).nonExportVariants;
      if (nonExportVariants is AsyncData) {
        variants = [...variants, ...nonExportVariants.value!];
      }
    }

    final filteredCategory =
        ref.read(catalogCategoryFilterProvider).toLowerCase();
    final searchTerm = ref.read(catalogSearchProvider).toLowerCase();
    final filteredBrand = ref.read(catalogBrandFilterProvider).toLowerCase();
    final filteredPrice = ref.read(catalogPriceFilterProvider);
    final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;

    if (filteredCategory == 'all' &&
        searchTerm.isEmpty &&
        filteredBrand.isEmpty &&
        filteredPrice == null) {
      return variants;
    }

    var filteredVariants = variants.where((variant) {
      final category = (variant.category ?? '').toLowerCase();
      return filteredCategory == 'all' || filteredCategory == category;
    }).toList();

    if (searchTerm.isNotEmpty) {
      filteredVariants.retainWhere((variant) {
        final name = (variant.name ?? '').toLowerCase();
        final category = (variant.category ?? '').toLowerCase();
        return name.contains(searchTerm) || category.contains(searchTerm);
      });
    }

    if (filteredBrand.isNotEmpty) {
      filteredVariants.retainWhere((variant) =>
          (variant.brandName ?? '').toLowerCase() == filteredBrand);
    }

    if (filteredPrice != null) {
      filteredVariants.sort((a, b) {
        final aPrice = isExportCountry ? supplierPrice(ref, a) : (a.price ?? 0);
        final bPrice = isExportCountry ? supplierPrice(ref, b) : (b.price ?? 0);
        return filteredPrice == CatalogPriceFilter.lowToHigh
            ? aPrice.compareTo(bPrice)
            : bPrice.compareTo(aPrice);
      });
    }

    return filteredVariants;
  }

  Widget _buildItemsGrid(List<Variant> variants, bool loading) {
    final pm = ProductsManager(variants);
    var variantCategories = pm.groupByCategory().entries.toList();

    return Consumer(
      builder: (context, ref, child) {
        final showingOtherProducts =
            ref.watch(cartProvider).showingOtherProducts;

        final otherVariants =
            ref.watch(orderControllerProvider.notifier).nonExportVariants;

        // showing more product variants on the catalog screen
        if (showingOtherProducts && otherVariants is AsyncData) {
          final filteredVariants = _filteredVariants(otherVariants.value!, ref);
          final pm = ProductsManager(filteredVariants);
          final otherVariantCategories = pm.groupByCategory().entries.toList();

          variantCategories = [...variantCategories, ...otherVariantCategories];
        }

        return ListView.builder(
          itemCount: variantCategories.length + 1,
          itemBuilder: (context, index) {
            if (index == variantCategories.length) {
              return const ShowMoreButtonWidget();
            }

            final category = variantCategories[index];
            return Padding(
              padding: const EdgeInsets.only(left: 35, right: 20),
              child: VariantCategoryItem(
                category.key,
                category.value,
                loading,
                isLastIndex: index == variantCategories.length - 1,
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildTableView(BuildContext context, List<Variant> variants) {
    return Consumer(
      builder: (context, ref, child) {
        final showingOtherProducts =
            ref.watch(cartProvider).showingOtherProducts;
        final otherVariants =
            ref.watch(orderControllerProvider.notifier).nonExportVariants;

        // showing more product variants on the catalog screen
        if (showingOtherProducts && otherVariants is AsyncData) {
          final filteredVariants = _filteredVariants(otherVariants.value!, ref);
          variants = [...variants, ...filteredVariants];
        }

        return Column(
          children: [
            _buildTableHeader(context, ref),
            Expanded(
              child: CustomScrollView(
                slivers: [
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        if (index == variants.length) {
                          return const ShowMoreButtonWidget();
                        }

                        final variant = variants[index];
                        return HoverableContainer(
                          // key: UniqueKey(),
                          index: index,
                          isLastIndex: index == variants.length - 1,
                          margin: const EdgeInsets.only(right: 20),
                          builder: (isHovered) => InkWell(
                            onTap: () => showCustomGeneralDialog(
                              context,
                              child: VariantDetailsWidget(variant),
                              percentage: 0.44,
                              minRightSectionWidth: 630,
                            ),
                            child: _buildTableRow(
                                variant, isHovered, context, ref),
                          ),
                        );
                      },
                      childCount: variants.length + 1,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTableHeader(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;
    final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;

    return Padding(
      padding: const EdgeInsets.only(right: 20, top: 20),
      child: Container(
        alignment: Alignment.centerLeft,
        child: Table(
          defaultVerticalAlignment: TableCellVerticalAlignment.bottom,
          columnWidths: {
            0: const FlexColumnWidth(1.2),
            1: const FlexColumnWidth(6),
            2: FlexColumnWidth(isExportCountry ? 4 : 3),
            3: FlexColumnWidth(isExportCountry ? 3 : 5),
            4: const FlexColumnWidth(3),
            5: const FlexColumnWidth(4),
            6: const FlexColumnWidth(2),
            7: const FlexColumnWidth(8),
          },
          children: [
            TableRow(
              decoration: BoxDecoration(
                border: Border(bottom: BorderSide(color: Palette.stroke)),
              ),
              children: [
                Container(),
                _buildHeaderCell('Product name', textTheme),
                _buildHeaderCell(isExportCountry ? 'EAN' : 'SKU', textTheme),
                _buildHeaderCell(
                    isExportCountry ? 'Produced in' : 'Inventory', textTheme),
                _buildHeaderCell('Category', textTheme),
                _buildHeaderCell(
                    isExportCountry ? 'Ships from' : 'Vendor', textTheme),
                _buildHeaderCell('Price', textTheme),
                Container(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCell(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 5),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _buildTableRow(
      Variant variant, bool isHovered, BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;
    final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;
    final itemInCart = ref.watch(cartItemProvider(variant.variantSupplierId));
    final incoterm = ref
            .read(userControllerProvider)
            ?.currentRetailOutlet
            ?.incoterms
            ?.toUpperCase() ??
        '-';

    return Table(
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      columnWidths: {
        0: const FlexColumnWidth(1.2),
        1: const FlexColumnWidth(6),
        2: FlexColumnWidth(isExportCountry ? 4 : 3),
        3: FlexColumnWidth(isExportCountry ? 3 : 5),
        4: const FlexColumnWidth(3),
        5: const FlexColumnWidth(4),
        6: const FlexColumnWidth(2),
        7: const FlexColumnWidth(8),
      },
      children: [
        TableRow(
          decoration: BoxDecoration(
            border: Border(bottom: BorderSide(color: Palette.stroke)),
          ),
          children: [
            Container(),
            _buildContentCell(
              Row(
                children: [
                  SizedBox(
                    width: 44,
                    height: 44,
                    child: CachedImage(variant.variantId, ImageSize.small),
                  ),
                  const Gap(5),
                  Flexible(
                    child: Text(
                      variant.description ?? variant.name ?? '-',
                      style: textTheme.bodyMedium
                          ?.copyWith(color: Palette.blackSecondary),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const Gap(5),
                ],
              ),
              textTheme,
            ),
            _buildContentCell(
                isExportCountry
                    ? variant.subUnit?.upc ?? '-'
                    : variant.code ?? '-',
                textTheme,
                maxLines: 2),
            _buildContentCell(
                isExportCountry
                    ? variant.productCountry ?? '-'
                    : variant.productName ?? '-',
                textTheme,
                maxLines: 2),
            _buildContentCell(variant.category ?? '-', textTheme, maxLines: 2),
            _buildContentCell(
              isExportCountry
                  ? Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: loading ? null : Palette.k0679FF,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            incoterm,
                            style: textTheme.bodySmall
                                ?.copyWith(color: Colors.white, fontSize: 12),
                          ),
                        ),
                        const Gap(5),
                        Text(variant.shipsFrom, style: textTheme.bodySmall),
                      ],
                    )
                  : variant.brandName ?? '-',
              textTheme,
            ),
            _buildContentCell(
              VariantPriceWidget(
                variant,
                textStyle:
                    textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              textTheme,
              ignoreSpacing: true,
            ),
            Align(
              alignment: Alignment.centerRight,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  const Gap(20),
                  Expanded(
                    child: StatefulBuilder(
                      builder: (context, setState) {
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            (itemInCart != null || isHovered)
                                ? VariantQuantityPicker(
                                    width: 100,
                                    variant,
                                    useEditingCart: false,
                                    showInitialAddBtn: false,
                                  )
                                : Container(),
                            OutlinedButton(
                              onPressed: () async {
                                await addToCart(variant, ref);
                                setState(() {});
                              },
                              style: OutlinedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 0, horizontal: 10),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                side: BorderSide(
                                    color: Palette.primaryBlack, width: 2),
                              ),
                              child: Text(
                                'Add to cart',
                                style: textTheme.bodyMedium
                                    ?.copyWith(fontWeight: FontWeight.w500),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> addToCart(Variant variant, WidgetRef ref) async {
    ref.read(cartProvider.notifier).addItem(
          CartItem(
            count:
                (ref.read(cartItemProvider(variant.variantSupplierId))?.count ??
                        0) +
                    1,
            variant: variant,
          ),
        );
  }

  Widget _buildContentCell(dynamic content, TextTheme textTheme,
      {bool ignoreSpacing = false, int maxLines = 2}) {
    return SizedBox(
      height: 64,
      child: Row(
        children: [
          Flexible(
            child: content is String
                ? Text(
                    content,
                    style: textTheme.bodyMedium
                        ?.copyWith(color: Palette.blackSecondary),
                    maxLines: maxLines,
                    overflow: TextOverflow.ellipsis,
                  )
                : content,
          ),
          if (!ignoreSpacing) const Gap(12),
        ],
      ),
    );
  }
}

// final catalogViewProvider =
//     StateProvider<CatalogViewType>((_) => CatalogViewType.grid);
// final catalogSearchProvider = StateProvider<String>((_) => '');
// final catalogCategoryFilterProvider = StateProvider<String>((_) => 'All');
// final catalogBrandFilterProvider = StateProvider<String>((_) => '');
// final catalogPriceFilterProvider =
//     StateProvider<CatalogPriceFilter?>((_) => null);

// enum CatalogPriceFilter {
//   lowToHigh('Low to High'),
//   highToLow('High to Low');

//   final String name;

//   const CatalogPriceFilter(this.name);

//   @override
//   String toString() => name;
// }

// class CatalogTableWidget extends ConsumerWidget {
//   const CatalogTableWidget(this.variants, this.loading, {super.key});

//   final List<Variant> variants;
//   final bool loading;

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final viewType = ref.watch(catalogViewProvider);
//     ref.watch(catalogSearchProvider);
//     ref.watch(catalogCategoryFilterProvider);
//     ref.watch(catalogPriceFilterProvider);
//     ref.watch(catalogBrandFilterProvider);

//     Widget child = const SizedBox.shrink();

//     final filteredVariants = _filteredVariants(variants, ref);
//     final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;

//     if (filteredVariants.isEmpty) {
//       return const CustomScrollView(
//         slivers: [
//           SliverFillRemaining(
//             hasScrollBody: false,
//             child: EmptyWidget(
//               icon: '$kSvgDir/order/cart.svg',
//               title: 'No products',
//               subTitle: 'There are no products matching your filter criteria',
//               // title: 'No products available',
//               // subTitle:
//               //     'There are currently no available\nproducts in the inventory',
//               baseline: false,
//             ),
//           ),
//         ],
//       );
//     }

//     viewType == CatalogViewType.grid
//         ? child = _buildItemsGrid(ref, filteredVariants, loading)
//         : child = Column(
//             children: [
//               _buildTableHeader(context, ref, filteredVariants),
//               Expanded(
//                 child: CustomScrollView(
//                   slivers: [
//                     SliverList(
//                       delegate: SliverChildBuilderDelegate(
//                         (context, index) {
//                           final variant = filteredVariants[index];
//                           return HoverableContainer(
//                             key: UniqueKey(),
//                             index: index,
//                             isLastIndex: index == filteredVariants.length - 1,
//                             margin: const EdgeInsets.only(right: 20),
//                             builder: (isHovered) => InkWell(
//                               onTap: () => showCustomGeneralDialog(
//                                 context,
//                                 child: VariantDetailsWidget(variant),
//                                 percentage: 0.44,
//                                 minRightSectionWidth: 630,
//                               ),
//                               child: _buildTableRow(
//                                   variant, isHovered, context, ref),
//                             ),
//                           );
//                         },
//                         childCount: filteredVariants.length,
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           );

//     return AnimatedSwitcherWidget(
//       child: child,
//     );
//   }

//   List<Variant> _filteredVariants(List<Variant> variants, WidgetRef ref) {
//     final filteredCategory =
//         ref.read(catalogCategoryFilterProvider).toLowerCase();
//     final searchTerm = ref.read(catalogSearchProvider).toLowerCase();
//     final filteredBrand = ref.read(catalogBrandFilterProvider).toLowerCase();
//     final filteredPrice = ref.read(catalogPriceFilterProvider);
//     final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;

//     // Early return if no filters are applied
//     if (filteredCategory == 'all' &&
//         searchTerm.isEmpty &&
//         filteredBrand.isEmpty &&
//         filteredPrice == null) {
//       return variants;
//     }

//     // Filter by category
//     final filteredVariants = variants.where((variant) {
//       final category = (variant.category ?? '').toLowerCase();
//       return filteredCategory == 'all' || filteredCategory == category;
//     }).toList();

//     // Filter by search term
//     if (searchTerm.isNotEmpty) {
//       filteredVariants.retainWhere((variant) {
//         final name = (variant.name ?? '').toLowerCase();
//         final category = (variant.category ?? '').toLowerCase();
//         return name.contains(searchTerm) || category.contains(searchTerm);
//       });
//     }

//     // Filter by brand
//     if (filteredBrand.isNotEmpty) {
//       filteredVariants.retainWhere((variant) =>
//           (variant.brandName ?? '').toLowerCase() ==
//           filteredBrand.toLowerCase());
//     }

//     // Sort by price if applicable
//     if (filteredPrice != null) {
//       filteredVariants.sort((a, b) {
//         final aPrice = isExportCountry ? supplierPrice(ref, a) : (a.price ?? 0);
//         final bPrice = isExportCountry ? supplierPrice(ref, b) : (b.price ?? 0);

//         return filteredPrice == CatalogPriceFilter.lowToHigh
//             ? aPrice.compareTo(bPrice)
//             : bPrice.compareTo(aPrice);
//       });
//     }

//     return filteredVariants;
//   }

//   Widget _buildItemsGrid(WidgetRef ref, List<Variant> variants, bool loading) {
//     final manager = ProductsManager(variants);
//     final variantCategories = manager.groupByCategory().entries.toList();

//     return ListView.builder(
//       itemCount: variantCategories.length,
//       itemBuilder: (context, index) {
//         final category = variantCategories[index];
//         return Padding(
//           padding: const EdgeInsets.only(left: 35, right: 20),
//           child: VariantCategoryItem(
//             // key: UniqueKey(),
//             category.key,
//             category.value,
//             loading,
//             isLastIndex: index == variantCategories.length - 1,
//           ),
//         );
//       },
//     );
//   }

//   Widget _buildTableHeader(
//       BuildContext context, WidgetRef ref, List<Variant> variants) {
//     final textTheme = Theme.of(context).textTheme;
//     final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;
//     return Padding(
//       padding: const EdgeInsets.only(right: 20, top: 20),
//       child: Container(
//         // height: 36,
//         // padding: const EdgeInsets.symmetric(vertical: 10),
//         alignment: Alignment.centerLeft,
//         child: Table(
//           defaultVerticalAlignment: TableCellVerticalAlignment.bottom,
//           columnWidths: {
//             0: const FlexColumnWidth(1.2),
//             1: const FlexColumnWidth(6),
//             2: FlexColumnWidth(isExportCountry ? 4 : 3),
//             3: FlexColumnWidth(isExportCountry ? 3 : 5),
//             4: const FlexColumnWidth(3),
//             5: const FlexColumnWidth(4),
//             6: const FlexColumnWidth(2),
//             7: const FlexColumnWidth(8),
//           },
//           children: [
//             TableRow(
//               decoration: BoxDecoration(
//                 border: Border(bottom: BorderSide(color: Palette.stroke)),
//               ),
//               children: [
//                 Container(),
//                 _buildHeaderCell('Product name', textTheme),
//                 _buildHeaderCell(isExportCountry ? 'EAN' : 'SKU', textTheme),
//                 _buildHeaderCell(
//                     isExportCountry ? 'Produced in' : 'Inventory', textTheme),
//                 _buildHeaderCell('Category', textTheme),
//                 _buildHeaderCell(
//                     isExportCountry ? 'Ships from' : 'Vendor', textTheme),
//                 _buildHeaderCell('Price', textTheme),
//                 Container(), // Empty space for floating icon
//               ],
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildHeaderCell(String text, TextTheme textTheme) {
//     return Padding(
//       padding: const EdgeInsets.only(bottom: 5),
//       child: Text(
//         text,
//         style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
//       ),
//     );
//   }

//   Widget _buildTableRow(
//     Variant variant,
//     bool isHovered,
//     BuildContext context,
//     WidgetRef ref,
//   ) {
//     final textTheme = Theme.of(context).textTheme;
//     final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;

//     final itemInCart =
//         ref.read(cartProvider.notifier).item(variant.variantSupplierId);

//     final incoterm = ref
//             .read(userControllerProvider)
//             ?.currentRetailOutlet
//             ?.incoterms
//             ?.toUpperCase() ??
//         '-';

//     return Table(
//       defaultVerticalAlignment: TableCellVerticalAlignment.middle,
//       columnWidths: {
//         0: const FlexColumnWidth(1.2),
//         1: const FlexColumnWidth(6),
//         2: FlexColumnWidth(isExportCountry ? 4 : 3),
//         3: FlexColumnWidth(isExportCountry ? 3 : 5),
//         4: const FlexColumnWidth(3),
//         5: const FlexColumnWidth(4),
//         6: const FlexColumnWidth(2),
//         7: const FlexColumnWidth(8),
//       },
//       children: [
//         TableRow(
//           decoration: BoxDecoration(
//               border: Border(
//             bottom: BorderSide(color: Palette.stroke),
//           )),
//           children: [
//             Container(),
//             _buildContentCell(
//               Row(
//                 children: [
//                   SizedBox(
//                     width: 44,
//                     height: 44,
//                     child: CachedImage(variant.variantId, ImageSize.small),
//                   ),
//                   const Gap(5),
//                   Flexible(
//                       child: Text(
//                     variant.description ?? variant.name ?? '-',
//                     style: textTheme.bodyMedium?.copyWith(
//                       color: Palette.blackSecondary,
//                     ),
//                     maxLines: 2,
//                     overflow: TextOverflow.ellipsis,
//                   )),
//                   const Gap(5),
//                 ],
//               ),
//               textTheme,
//             ),
//             _buildContentCell(
//                 isExportCountry
//                     ? variant.subUnit?.upc ?? '-'
//                     : variant.code ?? '-',
//                 textTheme,
//                 maxLines: 2),
//             _buildContentCell(
//                 isExportCountry
//                     ? variant.productCountry ?? '-'
//                     : variant.productName ?? '-',
//                 textTheme,
//                 maxLines: 2),
//             _buildContentCell(variant.category ?? '-', textTheme, maxLines: 2),
//             _buildContentCell(
//                 isExportCountry
//                     ? Row(
//                         children: [
//                           Container(
//                             padding: const EdgeInsets.symmetric(
//                                 horizontal: 8, vertical: 2),
//                             decoration: BoxDecoration(
//                               color: loading ? null : Palette.k0679FF,
//                               borderRadius: BorderRadius.circular(4),
//                             ),
//                             child: Text(
//                               incoterm,
//                               style: textTheme.bodySmall
//                                   ?.copyWith(color: Colors.white, fontSize: 12),
//                             ),
//                           ),
//                           const Gap(5),
//                           Text(variant.shipsFrom, style: textTheme.bodySmall)
//                         ],
//                       )
//                     : variant.brandName ?? '-',
//                 textTheme),
//             _buildContentCell(
//               VariantPriceWidget(
//                 variant,
//                 textStyle:
//                     textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
//               ),
//               textTheme,
//               ignoreSpacing: true,
//             ),
//             Align(
//               alignment: Alignment.centerRight,
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.end,
//                 children: [
//                   const Gap(20),
//                   Expanded(
//                     child: StatefulBuilder(
//                       builder: (context, setState) {
//                         return Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             (itemInCart != null || isHovered)
//                                 ? VariantQuantityPicker(
//                                     width: 100,
//                                     // key: UniqueKey(),
//                                     variant,
//                                     useEditingCart: false,
//                                     showInitialAddBtn: false,
//                                   )
//                                 : Container(),
//                             // const Gap(30),
//                             OutlinedButton(
//                               onPressed: () async {
//                                 await addToCart(variant, ref);
//                                 setState(() {
//                                   // rebuild scoped widget
//                                 });
//                               },
//                               style: OutlinedButton.styleFrom(
//                                 padding: const EdgeInsets.symmetric(
//                                     vertical: 0, horizontal: 10),
//                                 shape: RoundedRectangleBorder(
//                                   borderRadius: BorderRadius.circular(8),
//                                 ),
//                                 side: BorderSide(
//                                     color: Palette.primaryBlack, width: 2),
//                               ),
//                               child: Text(
//                                 'Add to cart',
//                                 style: textTheme.bodyMedium?.copyWith(
//                                   fontWeight: FontWeight.w500,
//                                 ),
//                               ),
//                             )
//                           ],
//                         );
//                       },
//                     ),
//                   )
//                 ],
//               ),
//             )
//           ],
//         ),
//       ],
//     );
//   }

//   // void addToCart(Variant variant, WidgetRef ref) {
//   //   final cartItem =
//   //       ref.read(cartProvider.notifier).item(variant.variantSupplierId);
//   //   num count = cartItem?.count ?? 0;
//   //   final updatedItem = CartItem(count: count += 1, variant: variant);
//   //   ref.read(cartProvider.notifier).addItem(updatedItem);
//   // }

//   Future<void> addToCart(Variant variant, WidgetRef ref) async {
//     ref.read(cartProvider.notifier).addItem(
//           CartItem(
//             count:
//                 (ref.read(cartItemProvider(variant.variantSupplierId))?.count ??
//                         0) +
//                     1,
//             variant: variant,
//           ),
//         );

//     return;
//   }

//   Widget _buildContentCell(dynamic content, TextTheme textTheme,
//       {bool? ignoreSpacing = false, int? maxLines = 2}) {
//     return SizedBox(
//       // padding: const EdgeInsets.symmetric(vertical: 6),
//       height: 64,
//       child: Row(
//         children: [
//           Flexible(
//             child: content is String
//                 ? Text(
//                     content,
//                     style: textTheme.bodyMedium?.copyWith(
//                       color: Palette.blackSecondary,
//                     ),
//                     maxLines: maxLines,
//                     overflow: TextOverflow.ellipsis,
//                   )
//                 : content,
//           ),
//           if (!ignoreSpacing!) const Gap(12),
//         ],
//       ),

//       // content is String
//       //     ? Padding(
//       //         padding: const EdgeInsets.only(right: 14),
//       //         child: Text(
//       //           content,
//       //           style: textTheme.bodyMedium?.copyWith(
//       //             color: Palette.blackSecondary,
//       //           ),
//       //           textAlign: centered ? TextAlign.center : TextAlign.left,
//       //           // overflow: TextOverflow.ellipsis,
//       //         ),
//       //       )
//       //     : content,
//     );
//   }
// }

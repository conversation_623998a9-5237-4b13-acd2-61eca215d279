import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/shipment.dart';
import 'package:td_procurement/src/components/widgets/cached_image.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class ItemsWidget extends ConsumerWidget {
  const ItemsWidget(this.shipment, {super.key});

  final Shipment shipment;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.maxFinite,
      decoration: BoxDecoration(
          border: Border.all(color: Palette.stroke),
          borderRadius: BorderRadius.circular(10)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 14),
            child: Text(
              'Documents',
              style: textTheme.headlineSmall,
            ),
          ),
          const Gap(16),
          _buildTableHeader(context),
          _buildTableRow(shipment, false, textTheme),
        ],
      ),
    );
  }

  Widget _buildTableHeader(context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      color: Palette.kF7F7F7,
      // height: 36,
      padding: const EdgeInsets.symmetric(vertical: 10),
      alignment: Alignment.center,
      child: Table(
        columnWidths: const {
          0: FlexColumnWidth(5),
          1: FlexColumnWidth(5),
        },
        children: [
          TableRow(
            children: [
              _buildHeaderCell('Name', textTheme),
              _buildHeaderCell('Quantity', textTheme, true),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(String text, TextTheme textTheme,
      [bool centered = false]) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8)
          .copyWith(left: centered ? 0 : 60),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
        textAlign: centered ? TextAlign.center : TextAlign.left,
      ),
    );
  }

  Widget _buildTableRow(
    Shipment shipment,
    bool isHovered,
    TextTheme textTheme,
  ) {
    return Table(
      columnWidths: const {
        0: FlexColumnWidth(5),
        1: FlexColumnWidth(5),
      },
      children: [
        ...(shipment.items ?? []).map((el) {
          return TableRow(
            decoration: BoxDecoration(
                border: Border(
              bottom: BorderSide(color: Palette.stroke),
            )),
            children: [
              _buildContentCell(
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 44,
                        height: 44,
                        alignment: Alignment.topLeft,
                        child: CachedImage(el.variantId, ImageSize.small),
                      ),
                      const Gap(10),
                      Flexible(
                        child: Text(
                          el.name ?? '-',
                        ),
                      ),
                    ],
                  ),
                  textTheme),
              _buildContentCell('${el.quantity}', textTheme),
            ],
          );
        })
      ],
    );
  }

  Widget _buildContentCell(
    dynamic content,
    TextTheme textTheme,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: Center(
        child: content is String
            ? Text(
                content,
                style: textTheme.bodyMedium?.copyWith(
                  color: Palette.blackSecondary,
                ),
                overflow: TextOverflow.ellipsis,
              )
            : content,
      ),
    );
  }
}

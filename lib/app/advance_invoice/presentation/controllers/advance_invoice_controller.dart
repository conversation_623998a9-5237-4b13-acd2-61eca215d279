import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_flutter_core/use_cases/use_cases.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/advance_invoice_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/create_retail_invoice_param.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/line_item_param.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/upload_pod_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/use_cases/advance_invoice_use_cases.dart';
import 'package:td_procurement/app/auth/presentation/controllers/session_controller.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/core/models/optional.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

import 'advance_invoice_state.dart';

final advanceInvoiceControllerProvider =
    NotifierProvider<AdvanceInvoiceController, AdvanceInvoiceState>(
        () => AdvanceInvoiceController());

class AdvanceInvoiceController extends Notifier<AdvanceInvoiceState> {
  @override
  AdvanceInvoiceState build() {
    final session = ref.watch(sessionController);

    // If accessToken is null or empty (logged out), return initial state
    if (session.accessToken == null || session.accessToken!.isEmpty) {
      // ref.invalidate(createAdvanceInvoiceProvider);
      return AdvanceInvoiceState.initial();
    }

    return AdvanceInvoiceState.initial();
  }

  Future<void> fetchAdvanceInvoices(
    FetchAdvanceInvoiceParams params, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    state = state.copyWith(
        fetchAdvanceInvoiceParams: state.fetchAdvanceInvoiceParams
            .copyWith(activeIndex: params.activeIndex));

    if (forced) {
      state = state.copyWith(advanceInvoices: const AsyncLoading());
    } else {
      if (params.currentPage == 1 &&
          (state.advanceInvoices.hasValue &&
              state.advanceInvoices.value!.isNotEmpty)) {
        return;
      }

      // return if query has reached the max page or if loading or loading more orders
      // if (state.fetchAdvanceInvoiceParams.currentPage ==
      //         state.fetchAdvanceInvoiceParams.totalPages ||
      //     state.advanceInvoices.isLoading ||
      //     state.fetchAdvanceInvoiceParams.loadingMore) {
      //   return;
      // }

      if (state.fetchAdvanceInvoiceParams.loaded) {
        state = state.copyWith(
            fetchAdvanceInvoiceParams:
                state.fetchAdvanceInvoiceParams.copyWith(loadingMore: true));
      } else {
        state = state.copyWith(advanceInvoices: const AsyncLoading());
      }
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await ref.read(fetchAdvanceInvoicesUseCaseProvider(params));

      res.when(
        success: (data) {
          state = state.copyWith(
            advanceInvoices: AsyncData(
                [...?state.advanceInvoices.value, ...data.retailInvoices]),
            fetchAdvanceInvoiceParams: params
                .fromQueryParams(data.queryParams)
                .copyWith(loaded: true, loadingMore: false),
          );
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            if (!state.fetchAdvanceInvoiceParams.loaded) {
              state = state.copyWith(
                  advanceInvoices: AsyncError(e, StackTrace.current),
                  fetchAdvanceInvoiceParams: state.fetchAdvanceInvoiceParams
                      .copyWith(loaded: false, loadingMore: false));
            }
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  Future<void> fetchInvoiceDetails(
    String advanceInvoiceId, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    if (forced) {
      state = state.copyWith(advanceInvoiceDetails: const AsyncLoading());
    } else {
      state = state.copyWith(advanceInvoiceDetails: const AsyncLoading());
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res =
          await ref.read(fetchAdvanceInvoiceUseCaseProvider(advanceInvoiceId));

      res.when(
        success: (data) {
          state = state.copyWith(
            advanceInvoiceDetails: AsyncData(data),
          );
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            state = state.copyWith(
              advanceInvoiceDetails: AsyncError(e, StackTrace.current),
            );
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  Future<void> fetchAdvanceInvoiceSettings({
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    if (forced) {
      state = state.copyWith(settings: const AsyncLoading());
    } else {
      if (state.settings.hasValue &&
          state.settings.value != null &&
          state.settings.value!.isComplete) {
        return;
      } else {
        state = state.copyWith(settings: const AsyncLoading());
      }
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await ref
          .read(fetchAdvanceInvoiceSettingsUseCaseProvider(NoParams()));
      res.when(
        success: (data) {
          state = state.copyWith(settings: AsyncData(data));
          shouldRetry = false;
        },
        failure: (error, _) async {
          if (attempts > retryCount) {
            state =
                state.copyWith(settings: AsyncError(error, StackTrace.current));
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  Future<void> fetchLoanContract({
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    if (forced) {
      state = state.copyWith(loanContract: const AsyncLoading());
    } else {
      if (state.loanContract.hasValue && state.loanContract.value != null) {
        return;
      } else {
        state = state.copyWith(loanContract: const AsyncLoading());
      }
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await ref.read(getLoanContractUseCaseProvider(NoParams()));
      res.when(
        success: (data) {
          state = state.copyWith(loanContract: AsyncData(data));
          shouldRetry = false;
        },
        failure: (error, _) async {
          if (attempts > retryCount) {
            state = state.copyWith(
                loanContract: AsyncError(error, StackTrace.current));
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  Future<void> fetchBankAccounts({
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    if (forced) {
      state = state.copyWith(bankAccounts: const AsyncLoading());
    } else {
      if (state.bankAccounts.hasValue && state.bankAccounts.value!.isNotEmpty) {
        return;
      } else {
        state = state.copyWith(bankAccounts: const AsyncLoading());
      }
    }

    while (attempts <= retryCount && shouldRetry) {
      while (attempts <= retryCount && shouldRetry) {
        attempts++;

        final res = await ref.read(getBankAccountsUseCaseProvider(NoParams()));
        res.when(
          success: (data) {
            state = state.copyWith(bankAccounts: AsyncData(data));
            shouldRetry = false;
          },
          failure: (error, _) async {
            if (attempts > retryCount) {
              state = state.copyWith(
                  bankAccounts: AsyncError(error, StackTrace.current));
              shouldRetry = false;
            } else {
              await Future.delayed(Duration(seconds: delaySeconds));
            }
          },
        );
      }
    }
  }

  Future<void> fetchSettlementBanks({
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    if (forced) {
      state = state.copyWith(settlementBanks: const AsyncLoading());
    } else {
      if (state.settlementBanks.hasValue &&
          state.settlementBanks.value!.isNotEmpty) {
        return;
      } else {
        state = state.copyWith(settlementBanks: const AsyncLoading());
      }
    }

    while (attempts <= retryCount && shouldRetry) {
      while (attempts <= retryCount && shouldRetry) {
        attempts++;

        final res =
            await ref.read(getSettlementBanksUseCaseProvider(NoParams()));
        res.when(
          success: (data) {
            state = state.copyWith(settlementBanks: AsyncData(data));
            shouldRetry = false;
          },
          failure: (error, _) async {
            if (attempts > retryCount) {
              state = state.copyWith(
                  settlementBanks: AsyncError(error, StackTrace.current));
              shouldRetry = false;
            } else {
              await Future.delayed(Duration(seconds: delaySeconds));
            }
          },
        );
      }
    }
  }

  void setAdvanceInvoiceDateFilter(DateTime? startDate, DateTime? endDate) {
    state = state.copyWith(
      fetchAdvanceInvoiceParams: state.fetchAdvanceInvoiceParams.copyWith(
        selectedDates: Optional([startDate, endDate]),
      ),
    );
  }

  // ----------------------------------------------------------------------------
  // Local State Management for Create Advance Invoice
  // ----------------------------------------------------------------------------

  void addLineItem(LineItemParam lineItem) {
    final createdState = ref.read(createAdvanceInvoiceProvider);
    final updatedState =
        createdState.copyWith(lineItems: [...createdState.lineItems, lineItem]);
    ref.read(createAdvanceInvoiceProvider.notifier).state = updatedState;
  }

  void removeLineItem(LineItemParam lineItem) {
    final createdState = ref.read(createAdvanceInvoiceProvider);
    final updatedState = createdState.copyWith(
        lineItems: createdState.lineItems
            .where((item) => item.id != lineItem.id)
            .toList());
    ref.read(createAdvanceInvoiceProvider.notifier).state = updatedState;
  }

  void updateLineItem(LineItemParam updated) {
    final createdState = ref.read(createAdvanceInvoiceProvider);

    final lineItems = createdState.lineItems;

    final updatedState = createdState.copyWith(
        lineItems:
            lineItems.map((e) => e.id == updated.id ? updated : e).toList());
    ref.read(createAdvanceInvoiceProvider.notifier).state = updatedState;
  }

  void addCharge(AdditionalCharge charge) {
    final createdState = ref.read(createAdvanceInvoiceProvider);
    final updatedState =
        createdState.copyWith(charges: [...?createdState.charges, charge]);
    ref.read(createAdvanceInvoiceProvider.notifier).state = updatedState;
  }

  void updateCharge(AdditionalCharge updated) {
    final createdState = ref.read(createAdvanceInvoiceProvider);

    final charges = createdState.charges ?? [];

    final updatedState = createdState.copyWith(
        charges: charges.map((e) => e.id == updated.id ? updated : e).toList());
    ref.read(createAdvanceInvoiceProvider.notifier).state = updatedState;
  }

  void removeCharge(AdditionalCharge charge) {
    final createdState = ref.read(createAdvanceInvoiceProvider);
    final updatedState = createdState.copyWith(
        charges: createdState.charges!
            .where((item) => item.id != charge.id)
            .toList());
    ref.read(createAdvanceInvoiceProvider.notifier).state = updatedState;
  }

  void addTax(TaxRate tax) {
    final createdState = ref.read(createAdvanceInvoiceProvider);
    final updatedState =
        createdState.copyWith(taxes: [...?createdState.taxes, tax]);
    ref.read(createAdvanceInvoiceProvider.notifier).state = updatedState;
  }

  void updateTax(TaxRate updated) {
    final createdState = ref.read(createAdvanceInvoiceProvider);
    final taxes = createdState.taxes ?? [];

    final updatedState = createdState.copyWith(
        taxes: taxes.map((e) => e.id == updated.id ? updated : e).toList());
    ref.read(createAdvanceInvoiceProvider.notifier).state = updatedState;
  }

  void removeTax(TaxRate tax) {
    final createdState = ref.read(createAdvanceInvoiceProvider);
    final updatedState = createdState.copyWith(
        taxes: createdState.taxes!.where((item) => item.id != tax.id).toList());
    ref.read(createAdvanceInvoiceProvider.notifier).state = updatedState;
  }

  void updateRecipientCustomer(String recipientOutletId) {
    final createdState = ref.read(createAdvanceInvoiceProvider);
    final updatedState =
        createdState.copyWith(recipientOutletId: recipientOutletId);
    ref.read(createAdvanceInvoiceProvider.notifier).state = updatedState;
  }

  void addDiscount(Discount discount) {
    final createdState = ref.read(createAdvanceInvoiceProvider);
    final updatedState = createdState.copyWith(discount: Optional(discount));
    ref.read(createAdvanceInvoiceProvider.notifier).state = updatedState;
  }

  void updateDiscount(Discount updated) {
    final createdState = ref.read(createAdvanceInvoiceProvider);
    final updatedState = createdState.copyWith(discount: Optional(updated));
    ref.read(createAdvanceInvoiceProvider.notifier).state = updatedState;
  }

  void removeDiscount() {
    final createdState = ref.read(createAdvanceInvoiceProvider);
    final updatedState = createdState.copyWith(discount: const Optional(null));
    ref.read(createAdvanceInvoiceProvider.notifier).state = updatedState;
  }

  void addNote(String note) {
    final createdState = ref.read(createAdvanceInvoiceProvider);
    final updatedState = createdState.copyWith(note: Optional(note));
    ref.read(createAdvanceInvoiceProvider.notifier).state = updatedState;
  }

  void updateNote(String note) {
    final createdState = ref.read(createAdvanceInvoiceProvider);
    final updatedState = createdState.copyWith(note: Optional(note));
    ref.read(createAdvanceInvoiceProvider.notifier).state = updatedState;
  }

  void removeNote() {
    final createdState = ref.read(createAdvanceInvoiceProvider);
    final updatedState = createdState.copyWith(note: const Optional(null));
    ref.read(createAdvanceInvoiceProvider.notifier).state = updatedState;
  }

  void resetCreateAdvanceInvoiceParams() {
    final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
    final currencyCode = ref.read(currencyCodeProvider);

    ref.read(createAdvanceInvoiceProvider.notifier).state =
        CreateRetailInvoiceParam(
      id: '',
      issuerOutletId: outlet?.id ?? '',
      recipientOutletId: '',
      lineItems: const [],
      isAdvance: false,
      currencyCode: currencyCode,
    );
  }

  void toggleFinancing(bool isFinanced) {
    final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
    final walletAccount = outlet?.walletAccount;

    final colAccount = WalletBank(
      id: 'wallet',
      bankName: walletAccount?.colBankName ?? 'TradeDepot Wallet',
      accountNumber: walletAccount?.colAccountNumber,
      type: WalletBankType.payment,
      accountName: outlet?.outletBusinessName,
      accountId: walletAccount?.colCustomerId,
    );

    ref.read(createAdvanceInvoiceProvider.notifier).state = ref
        .read(createAdvanceInvoiceProvider)
        .copyWith(
            isAdvance: isFinanced,
            settlementBank:
                isFinanced ? Optional(colAccount) : const Optional(null),
            dueDate: isFinanced
                ? Optional(DateTime.now().add(const Duration(days: 30)))
                : const Optional(null));
  }

  void addSettlementBank(WalletBank bank) {
    ref.read(createAdvanceInvoiceProvider.notifier).state = ref
        .read(createAdvanceInvoiceProvider)
        .copyWith(settlementBank: Optional(bank));
  }

  void addDisbursementBank(WalletBank bank) {
    ref.read(createAdvanceInvoiceProvider.notifier).state = ref
        .read(createAdvanceInvoiceProvider)
        .copyWith(disbursementAccountId: Optional(bank.accountId ?? bank.id));
  }

  void setAdvanceInvoiceDetails(RetailInvoice invoice) {
    state = state.copyWith(
      advanceInvoiceDetails: AsyncData(invoice),
    );
  }

  void setDueDate(DateTime? date) {
    final isAdvance = ref.read(createAdvanceInvoiceProvider).isAdvance;
    ref.read(createAdvanceInvoiceProvider.notifier).state = ref
        .read(createAdvanceInvoiceProvider)
        .copyWith(
            dueDate: isAdvance
                ? Optional(DateTime.now().add(const Duration(days: 30)))
                : Optional(date));
  }

  Future<void> uploadPod(UploadPodParams params) async {
    final response = await ref.read(uploadPodUseCaseProvider(params));
    return response.when(
      success: (data) => data,
      failure: (error, code) => throw error,
    );
  }
}

final createAdvanceInvoiceProvider = StateProvider<CreateRetailInvoiceParam>(
  (ref) {
    final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
    final currencyCode = ref.read(currencyCodeProvider);
    return CreateRetailInvoiceParam(
      id: '',
      issuerOutletId: outlet?.id ?? '',
      recipientOutletId: '',
      lineItems: const [],
      isAdvance: false,
      currencyCode: currencyCode,
    );
  },
);

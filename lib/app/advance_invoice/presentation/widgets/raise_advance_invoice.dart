import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_commons_flutter/models/wallet_account.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/advance_invoice_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/create_retail_invoice_param.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/line_item_param.dart';
import 'package:td_procurement/app/advance_invoice/domain/use_cases/advance_invoice_use_cases.dart';
import 'package:td_procurement/app/advance_invoice/presentation/controllers/advance_invoice_controller.dart';
import 'package:td_procurement/app/advance_invoice/presentation/controllers/advance_invoice_state.dart';
import 'package:td_procurement/app/advance_invoice/presentation/widgets/due_date_picker.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/order/presentation/widgets/outlet_search_auto_complete.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';
import 'package:td_procurement/src/utils/validators/validators.dart';

class RaiseAdvanceInvoiceWidget extends ConsumerWidget {
  const RaiseAdvanceInvoiceWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final createParams = ref.watch(createAdvanceInvoiceProvider);
    final state = ref.watch(advanceInvoiceControllerProvider);
    final notifier = ref.read(advanceInvoiceControllerProvider.notifier);
    final loanContract = state.loanContract.asData?.value;
    final bankAccounts = state.bankAccounts.asData?.value ?? [];
    final textTheme = context.textTheme;

    // Add state for showing the add bank account form
    return _RaiseAdvanceInvoiceWidgetBody(
      createParams: createParams,
      state: state,
      notifier: notifier,
      loanContract: loanContract,
      bankAccounts: bankAccounts,
      textTheme: textTheme,
    );
  }
}

class _RaiseAdvanceInvoiceWidgetBody extends ConsumerStatefulWidget {
  const _RaiseAdvanceInvoiceWidgetBody({
    required this.createParams,
    required this.state,
    required this.notifier,
    required this.loanContract,
    required this.bankAccounts,
    required this.textTheme,
  });

  final CreateRetailInvoiceParam createParams;
  final AdvanceInvoiceState state;
  final AdvanceInvoiceController notifier;
  final LoanContract? loanContract;
  final List<WalletBank> bankAccounts;
  final TextTheme textTheme;

  @override
  ConsumerState<_RaiseAdvanceInvoiceWidgetBody> createState() =>
      _RaiseAdvanceInvoiceWidgetBodyState();
}

class _RaiseAdvanceInvoiceWidgetBodyState
    extends ConsumerState<_RaiseAdvanceInvoiceWidgetBody> {
  bool showAddBankForm = false;
  SettlementBank? selectedBank;
  final TextEditingController _accountNumberController =
      TextEditingController();
  final TextEditingController _accountNameController = TextEditingController();
  bool _consent = false;
  bool _isValidAccountName = false;
  bool _isValidating = false;
  bool _isSubmitting = false;
  String? _validationError;
  String? _submitError;
  final bankAccountFormController = ExpandableController();

  @override
  void dispose() {
    _accountNumberController.dispose();
    _accountNameController.dispose();
    super.dispose();
  }

  void _validateAccount() async {
    setState(() {
      _isValidAccountName = false;
      _isValidating = true;
      _validationError = null;
      _accountNameController.clear();
    });
    final bank = selectedBank;
    final number = _accountNumberController.text;
    if (bank == null || number.length != 10) {
      setState(() {
        _isValidating = false;
      });
      return;
    }
    final res =
        await ref.read(validateBankAccountUseCaseProvider((number, bank.code)));
    res.when(
      success: (data) {
        setState(() {
          _isValidating = false;
          _isValidAccountName = data.$1.isNotEmpty;
          _accountNameController.text = data.$1;
          _validationError = data.$1.isEmpty ? 'Account not found' : null;
        });
      },
      failure: (e, _) {
        setState(() {
          _isValidating = false;
          _validationError = e.toString();
        });
        if (mounted) {
          Toast.apiError(e, context);
        }
      },
    );
  }

  void _submit() async {
    setState(() {
      _isSubmitting = true;
      _submitError = null;
    });
    final bank = selectedBank;
    final number = _accountNumberController.text;
    final name = _accountNameController.text;
    if (bank == null || number.length != 10 || name.isEmpty || !_consent) {
      setState(() {
        _isSubmitting = false;
        _submitError = 'Please complete all fields.';
      });
      return;
    }
    final params = LinkBankParams(
      number,
      bank.code,
      name,
      bank.name,
      null,
      LinkBankType.terminal,
    );
    final res = await ref.read(linkBankAccountUseCaseProvider(params));
    res.when(
      success: (_) async {
        await ref
            .read(advanceInvoiceControllerProvider.notifier)
            .fetchBankAccounts(forced: true);
        setState(() {
          _isSubmitting = false;
          showAddBankForm = false;
          _accountNumberController.clear();
          _accountNameController.clear();
          _consent = false;
          _isValidAccountName = false;
          _validationError = null;
          _submitError = null;
          selectedBank = null;
        });
        if (mounted) {
          Toast.success('Bank account added successfully', context);
        }
      },
      failure: (e, _) {
        setState(() {
          _isSubmitting = false;
          _submitError = e.toString();
        });
      },
    );
  }

  void toggleBankFormVisibility(bool value) {
    setState(() {
      showAddBankForm = value;
      _accountNumberController.clear();
      _accountNameController.clear();
      _consent = false;
      _isValidAccountName = false;
      _validationError = null;
      _submitError = null;
      selectedBank = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = widget.textTheme;
    final settlementBanks = widget.state.settlementBanks.asData?.value ?? [];
    final loanContract = widget.state.loanContract.asData?.value;
    final canApplyForAdvance = loanContract?.canApplyForAdvance ?? false;
    final WalletAccount? wallet =
        ref.read(userControllerProvider)?.currentRetailOutlet?.walletAccount;
    final hasWalletAccount =
        wallet?.accountNumber != null && wallet?.bankName != null;
    final walletBank = WalletBank(
        id: 'wallet',
        accountId: 'wallet',
        bankName: wallet?.bankName,
        accountNumber: wallet?.accountNumber,
        bankCode: wallet?.bankCode);
    final disbursementBanks = hasWalletAccount
        ? [walletBank, ...widget.bankAccounts]
        : widget.bankAccounts;

    final title =
        canApplyForAdvance ? 'Payment & Financing Options' : 'Payment Options';

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 100),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const Gap(40),
          const OutletSearchAutoCompleteWidget(isAdvanceInvoice: true),
          const Gap(40),
          // Items Section
          const _ItemsSection(key: ValueKey('items')),
          const Gap(40),
          // Service Options Section (for Taxes/Charges)
          const _ItemsOptionsSection(
            key: ValueKey('items_options'),
          ),
          const Gap(40),
          // Payment & Financing Section
          Skeletonizer(
            enabled: ref
                .watch(advanceInvoiceControllerProvider)
                .loanContract
                .isLoading,
            child: Card(
              margin: EdgeInsets.zero,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              color: Colors.white,
              elevation: 0,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Palette.stroke),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                          fontSize: 18,
                          color: Palette.primaryBlack,
                        ),
                      ),
                      const Gap(16),
                      if (canApplyForAdvance) ...[
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Palette.kF7F7F7,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Palette.stroke),
                          ),
                          child: Row(
                            // mainAxisSize: MainAxisSize.min,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Apply Financing',
                                      style: textTheme.bodyLarge?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: Palette.primaryBlack,
                                      ),
                                    ),
                                    const Gap(4),
                                    Text(
                                      'Enable this to use TradeDepot financing for this invoice. If disabled, you must select a payment account.',
                                      style: textTheme.bodySmall?.copyWith(
                                        color: Palette.blackSecondary,
                                        height: 1.4,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const Gap(16),
                              Switch.adaptive(
                                value: widget.createParams.isAdvance,
                                activeColor: Palette.primary,
                                onChanged: (val) {
                                  widget.notifier.toggleFinancing(val);
                                  if (val) {
                                    setState(() {
                                      showAddBankForm = false;
                                    });
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                        const Gap(20),
                      ],
                      AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        transitionBuilder:
                            (Widget child, Animation<double> animation) {
                          return SlideTransition(
                            position: Tween<Offset>(
                              begin: const Offset(0, -0.1),
                              end: Offset.zero,
                            ).animate(CurvedAnimation(
                              parent: animation,
                              curve: Curves.easeInOut,
                            )),
                            child: FadeTransition(
                              opacity: animation,
                              child: child,
                            ),
                          );
                        },
                        child: widget.createParams.isAdvance
                            ? Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Disbursement Account',
                                      style: textTheme.bodyMedium),
                                  const Gap(4),
                                  Skeletonizer(
                                    enabled: ref
                                        .watch(advanceInvoiceControllerProvider)
                                        .bankAccounts
                                        .isLoading,
                                    child: SizedBox(
                                      // height: 32,
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          // const Gap(16),
                                          Flexible(
                                            flex: 1,
                                            fit: FlexFit.tight,
                                            child: SmoothExpandable(
                                              controller:
                                                  bankAccountFormController,
                                              title: 'Add Bank Account',
                                              initiallyExpanded:
                                                  showAddBankForm,
                                              onExpansionChanged:
                                                  toggleBankFormVisibility,
                                              content: _buildAddBankForm(
                                                context,
                                                textTheme,
                                                settlementBanks,
                                              ),
                                              leading:
                                                  AdvancedDropdown<WalletBank>(
                                                selectedValue: disbursementBanks
                                                        .isNotEmpty
                                                    ? disbursementBanks
                                                        .firstWhereOrNull((bank) =>
                                                            (bank.accountId !=
                                                                    null &&
                                                                bank.accountId ==
                                                                    widget
                                                                        .createParams
                                                                        .disbursementAccountId) ||
                                                            bank.id != null &&
                                                                bank.id ==
                                                                    widget
                                                                        .createParams
                                                                        .disbursementAccountId)
                                                    : null,
                                                options: disbursementBanks,
                                                hint:
                                                    'Select Disbursement Account',
                                                height: 32,
                                                itemToString: (bank) =>
                                                    '${bank.bankName} - ${bank.accountNumber ?? ''}',
                                                onChanged: (val) {
                                                  if (val != null) {
                                                    widget.notifier
                                                        .addDisbursementBank(
                                                            val);
                                                  }
                                                },
                                                searchable:
                                                    widget.bankAccounts.length >
                                                        5,
                                                menuMaxHeight: 250,
                                                spaceOffsetAbove:
                                                    widget.bankAccounts.isEmpty
                                                        ? 200
                                                        : null,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  const Gap(20),
                                  Container(
                                    key: const ValueKey('financing'),
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.blue.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                          color: Colors.blue
                                              .withValues(alpha: 0.3)),
                                    ),
                                    child: Row(
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            color: Colors.blue
                                                .withValues(alpha: 0.1),
                                            borderRadius:
                                                BorderRadius.circular(6),
                                          ),
                                          child: const Icon(
                                            Icons.percent,
                                            color: Colors.blue,
                                            size: 20,
                                          ),
                                        ),
                                        const Gap(12),
                                        Expanded(
                                          child: Text(
                                            widget.loanContract != null
                                                ? 'Your advance rate: ${loanContract?.advanceRate ?? 0}%'
                                                : 'Loading advance rate...',
                                            style:
                                                textTheme.bodyMedium?.copyWith(
                                              fontWeight: FontWeight.w500,
                                              color: Colors.blue.shade700,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              )
                            : Container(
                                key: const ValueKey('bank-selection'),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text('Payment Account',
                                        style: textTheme.bodyMedium),
                                    const Gap(4),
                                    Skeletonizer(
                                      enabled: ref
                                          .watch(
                                              advanceInvoiceControllerProvider)
                                          .bankAccounts
                                          .isLoading,
                                      child: SizedBox(
                                        // height: 32,
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            // const Gap(16),
                                            Flexible(
                                              flex: 1,
                                              fit: FlexFit.tight,
                                              child: SmoothExpandable(
                                                controller:
                                                    bankAccountFormController,
                                                title: 'Add Bank Account',
                                                initiallyExpanded:
                                                    showAddBankForm,
                                                onExpansionChanged:
                                                    toggleBankFormVisibility,
                                                content: _buildAddBankForm(
                                                  context,
                                                  textTheme,
                                                  settlementBanks,
                                                ),
                                                leading: AdvancedDropdown<
                                                    WalletBank>(
                                                  selectedValue: widget
                                                          .bankAccounts
                                                          .isNotEmpty
                                                      ? widget.bankAccounts
                                                          .firstWhereOrNull((bank) =>
                                                              bank.accountNumber ==
                                                              widget
                                                                  .createParams
                                                                  .settlementBank
                                                                  ?.accountNumber)
                                                      : null,
                                                  options: widget.bankAccounts,
                                                  hint:
                                                      'Select Payment Account',
                                                  height: 32,
                                                  itemToString: (bank) =>
                                                      '${bank.bankName} - ${bank.accountNumber ?? ''}',
                                                  onChanged: (val) {
                                                    if (val != null) {
                                                      widget.notifier
                                                          .addSettlementBank(
                                                              val);
                                                    }
                                                  },
                                                  searchable: widget
                                                          .bankAccounts.length >
                                                      5,
                                                  menuMaxHeight: 250,
                                                  spaceOffsetAbove: widget
                                                          .bankAccounts.isEmpty
                                                      ? 200
                                                      : null,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                      ),
                      if (!widget.createParams.isAdvance) ...[
                        // Payment Due Date UI
                        const Gap(20),
                        DueDatePickerOverlay(
                          selectedDate: widget.createParams.dueDate,
                          onDateSelected: (date) {
                            widget.notifier.setDueDate(date);
                          },
                          maxMonths: 24,
                          label: 'Payment Due Date',
                        ),
                        const Gap(20),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
          const Gap(40),
          // Summary Section
          // const _SummarySection(key: ValueKey('summary')),
          // Gap(40),
        ],
      ),
    );
  }

  Widget _buildAddBankForm(BuildContext context, TextTheme textTheme,
      List<SettlementBank> settlementBanks) {
    return Skeletonizer(
      enabled:
          ref.watch(advanceInvoiceControllerProvider).settlementBanks.isLoading,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Palette.stroke),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Add New Bank Account',
              style: textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Palette.primaryBlack,
              ),
            ),
            const Gap(16),
            AdvancedDropdown<SettlementBank>(
              selectedValue: selectedBank,
              options: settlementBanks,
              hint: 'Settlement Bank',
              itemToString: (bank) => bank.name,
              onChanged: (val) {
                setState(() {
                  selectedBank = val;
                });
                if (val != null && _accountNumberController.text.length == 10) {
                  _validateAccount();
                }
              },
              searchable: settlementBanks.length > 5,
              menuMaxHeight: 200,
              height: 34,
            ),
            const Gap(16),
            CompactInput(
              controller: _accountNumberController,
              hint: 'Enter 10-digit account number',
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(10),
              ],
              onEditingComplete: () {
                if (selectedBank != null &&
                    _accountNumberController.text.length == 10) {
                  _validateAccount();
                }
              },
              onChanged: (val) {
                if (val.length == 10 && selectedBank != null) {
                  _validateAccount();
                }
              },
            ),
            const Gap(16),
            Row(
              children: [
                Expanded(
                  child: CompactInput(
                    controller: _accountNameController,
                    hint: 'Account Name',
                    readOnly: true,
                  ),
                ),
                if (_isValidating)
                  const Padding(
                    padding: EdgeInsets.only(left: 8),
                    child: SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  ),
              ],
            ),
            const Gap(16),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Checkbox(
                  value: _consent,
                  onChanged: (val) {
                    setState(() {
                      _consent = val ?? false;
                    });
                  },
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity: VisualDensity.compact,
                ),
                const Gap(8),
                Expanded(
                  child: Text(
                    'I confirm that this bank account belongs to me and I authorize TradeDepot to verify this account.',
                    style: textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w400,
                      color: Palette.blackSecondary,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
            if (_submitError != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  _submitError!,
                  style: textTheme.bodySmall?.copyWith(
                    color: Palette.kE61010,
                  ),
                ),
              ),
            const Gap(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: _isSubmitting
                      ? null
                      : () {
                          toggleBankFormVisibility(false);
                          bankAccountFormController.toggle();
                        },
                  child: const Text('Cancel'),
                ),
                const Gap(8),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Palette.primary,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: _isSubmitting || !_consent || !_isValidAccountName
                      ? null
                      : _submit,
                  child: _isSubmitting
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Text('Add Account'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _ItemsSection extends ConsumerStatefulWidget {
  const _ItemsSection({super.key});
  @override
  ConsumerState<_ItemsSection> createState() => _ItemsSectionState();
}

class _ItemsSectionState extends ConsumerState<_ItemsSection> {
  bool showForm = false;
  bool showDiscountFields = false;
  String? editingItemId;
  final _nameController = TextEditingController();
  final _qtyController = TextEditingController();
  final _priceController = TextEditingController();
  final _taxController = TextEditingController();
  final _discountValueController = TextEditingController();
  DiscountType _selectedDiscountType = DiscountType.fixed;
  final itemsFormController = ExpandableController();
  final _nameFieldFocus = FocusNode();

  @override
  void dispose() {
    _nameController.dispose();
    _qtyController.dispose();
    _priceController.dispose();
    _taxController.dispose();
    _discountValueController.dispose();
    super.dispose();
  }

  void _startEditing(LineItemParam item) {
    if (!itemsFormController.isExpanded) {
      itemsFormController.toggle();
    }

    setState(() {
      editingItemId = item.id;
      _nameController.text = item.name;
      _qtyController.text = item.quantity.toString();
      _priceController.text = item.unitPrice.toString();
      _taxController.text = item.taxRate.toString();
      _discountValueController.text = item.discount?.value.toString() ?? '';
      _selectedDiscountType = item.discount?.type ?? DiscountType.fixed;
      showDiscountFields = item.discount != null;
      showForm = true;
    });

    _nameFieldFocus.unfocus();
  }

  void _saveItem() {
    final name = _nameController.text.trim();
    final quantity =
        int.tryParse(_qtyController.text.replaceAll(',', '').trim()) ?? 0;
    final price =
        double.tryParse(_priceController.text.replaceAll(',', '').trim()) ?? 0;
    final taxRate =
        double.tryParse(_taxController.text.replaceAll(',', '').trim()) ?? 0;

    if (name.isNotEmpty && quantity > 0 && price > 0) {
      // Create discount if value is provided and discount fields are shown
      Discount? discount;
      if (showDiscountFields && _discountValueController.text.isNotEmpty) {
        final value = double.tryParse(
                _discountValueController.text.replaceAll(',', '').trim()) ??
            0;
        if (value > 0 ||
            (_selectedDiscountType == DiscountType.percentage &&
                value >= 0 &&
                value <= 100)) {
          discount = Discount(
            type: _selectedDiscountType,
            value: value,
          );
        }
      }

      if (editingItemId != null) {
        // Update existing item
        final updatedItem = LineItemParam(
          id: editingItemId!,
          name: name,
          quantity: quantity,
          unitPrice: price,
          taxRate: taxRate,
          discount: discount,
        );
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .updateLineItem(updatedItem);
        setState(() {
          editingItemId = null;
          showForm = false;
          showDiscountFields = false;
          _nameController.clear();
          _qtyController.clear();
          _priceController.clear();
          _taxController.clear();
          _discountValueController.clear();
        });
      } else {
        // Add new item
        final newItem = LineItemParam(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: name,
          quantity: quantity,
          unitPrice: price,
          taxRate: taxRate,
          discount: discount,
        );
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .addLineItem(newItem);
        setState(() {
          showForm = false;
          showDiscountFields = false;
          _nameController.clear();
          _qtyController.clear();
          _priceController.clear();
          _taxController.clear();
          _discountValueController.clear();
        });
      }
    }

    if (_nameFieldFocus.canRequestFocus) {
      _nameFieldFocus.requestFocus();
    }
  }

  void _cancelEdit() {
    setState(() {
      editingItemId = null;
      showForm = false;
      showDiscountFields = false;
      _nameController.clear();
      _qtyController.clear();
      _priceController.clear();
      _taxController.clear();
      _discountValueController.clear();
    });
  }

  void toggleItemsFormVisibility(bool value) {
    setState(() {
      editingItemId = null;
      showForm = value;
      showDiscountFields = false;
      _nameController.clear();
      _qtyController.clear();
      _priceController.clear();
      _taxController.clear();
      _discountValueController.clear();
    });

    if (_nameFieldFocus.canRequestFocus) {
      _nameFieldFocus.requestFocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final lineItems = ref.watch(createAdvanceInvoiceProvider).lineItems;
    final currencyCode = ref.read(currencyCodeProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Items',
          style: textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 18,
            color: Palette.primaryBlack,
          ),
        ),
        const Gap(16),
        // Add Item Section with smooth expansion
        SmoothExpandable(
          controller: itemsFormController,
          title: 'Add Item',
          initiallyExpanded: showForm,
          onExpansionChanged: toggleItemsFormVisibility,
          content: _buildItemForm(context, textTheme, currencyCode),
        ),
        const Gap(16),
        // Items List
        _buildItemsList(context, textTheme, currencyCode, lineItems),
      ],
    );
  }

  Widget _buildItemForm(
    BuildContext context,
    TextTheme textTheme,
    String currencyCode,
  ) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Row(
          children: [
            Expanded(
              child: CompactInput(
                controller: _nameController,
                hint: 'Item name',
                autofocus: true,
                focusNode: _nameFieldFocus,
              ),
            ),
            const Gap(12),
            Expanded(
              child: CompactInput(
                controller: _qtyController,
                hint: 'Quantity',
                keyboardType: TextInputType.number,
                inputFormatters: [Validators.formatInput()],
              ),
            ),
            const Gap(12),
            Expanded(
              child: CompactInput(
                controller: _priceController,
                hint: 'Unit Price',
                prefix: '${CurrencyWidget.symbol(context, currencyCode)} ',
                keyboardType: TextInputType.number,
                inputFormatters: [Validators.formatCurrency(decimalPlaces: 2)],
              ),
            ),
            const Gap(12),
            Expanded(
              child: CompactInput(
                controller: _taxController,
                hint: 'Tax (%)',
                suffix: '%',
                keyboardType: TextInputType.number,
                inputFormatters: [Validators.formatRange(0, 100)],
                onEditingComplete: () {
                  if (!showDiscountFields) {
                    _saveItem();
                  }
                },
              ),
            ),
          ],
        ),
        const Gap(10),
        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if (!showDiscountFields)
              Expanded(
                child: Row(
                  // mainAxisSize: MainAxisSize.min,
                  children: [
                    Transform.scale(
                      scale: 0.7,
                      child: Checkbox(
                        value: false,
                        onChanged: (value) {
                          setState(() {
                            showDiscountFields = true;
                          });
                        },
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        visualDensity: VisualDensity.compact,
                      ),
                    ),
                    Text(
                      'Add discount',
                      style: textTheme.bodySmall?.copyWith(
                        color: Palette.primary,
                      ),
                    ).onTapNoFeedback(() {
                      setState(() {
                        showDiscountFields = true;
                      });
                    }),
                  ],
                ),
              )
            else ...[
              Expanded(
                flex: 2,
                child: AdvancedDropdown<DiscountType>(
                  selectedValue: _selectedDiscountType,
                  options: DiscountType.values,
                  hint: 'Type',
                  itemToString: (type) =>
                      type == DiscountType.fixed ? 'Fixed' : 'Percentage',
                  onChanged: (type) {
                    if (type != null) {
                      setState(() {
                        _selectedDiscountType = type;
                        _discountValueController.clear();
                      });
                    }
                  },
                  searchable: false,
                  menuMaxHeight: 100,
                  isExpanded: false,
                  height: 34,
                ),
              ),
              const Gap(12),
              Expanded(
                flex: 2,
                child: CompactInput(
                  controller: _discountValueController,
                  hint: _selectedDiscountType == DiscountType.fixed
                      ? 'Amount'
                      : 'Value (%)',
                  prefix: _selectedDiscountType == DiscountType.fixed
                      ? '${CurrencyWidget.symbol(context, currencyCode)} '
                      : null,
                  suffix: _selectedDiscountType == DiscountType.percentage
                      ? '%'
                      : null,
                  keyboardType: TextInputType.number,
                  inputFormatters:
                      _selectedDiscountType == DiscountType.percentage
                          ? [Validators.formatRange(0, 100)]
                          : [Validators.formatCurrency(decimalPlaces: 2)],
                  onEditingComplete: _saveItem,
                ),
              ),
              const Gap(8),
              IconButton(
                icon: const Icon(Icons.close, size: 14),
                onPressed: () {
                  setState(() {
                    showDiscountFields = false;
                    _discountValueController.clear();
                  });
                },
              ),
            ],
            // const Spacer(),
            TextButton(
              style: TextButton.styleFrom(
                minimumSize: const Size(0, 36),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              onPressed: () {
                _cancelEdit();
                itemsFormController.toggle();
              },
              child: const Text('Cancel'),
            ),
            const Gap(8),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(0, 36),
                backgroundColor: Palette.primary,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              onPressed: _saveItem,
              child: Text(editingItemId != null ? 'Update' : 'Save'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildItemsList(BuildContext context, TextTheme textTheme,
      String currencyCode, List<LineItemParam> lineItems) {
    if (lineItems.isEmpty) {
      return Center(
        child: Text(
          'No items added yet',
          style: textTheme.bodyMedium?.copyWith(
            color: Palette.placeholder,
            fontStyle: FontStyle.italic,
          ),
        ),
      );
    }

    return Column(
      children: lineItems.mapIndexed((index, item) {
        return ListTile(
          dense: true,
          title: Text(
            item.name,
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Palette.primaryBlack,
            ),
          ),
          subtitle: Text(
            'Qty: ${item.quantity} × ${CurrencyWidget.value(context, currencyCode, item.unitPrice)}',
            style: textTheme.bodySmall?.copyWith(
              color: Palette.blackSecondary,
            ),
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (item.discount != null)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    color: Palette.blackSecondary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    item.discount!.type == DiscountType.fixed
                        ? CurrencyWidget.value(
                            context, currencyCode, item.discount!.value)
                        : '${item.discount!.value}%',
                    style: textTheme.bodySmall?.copyWith(
                      color: Palette.blackSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              Text(
                CurrencyWidget.value(
                    context, currencyCode, (item.quantity * item.unitPrice)),
                style: textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Palette.primaryBlack,
                ),
              ),
              const Gap(8),
              IconButton(
                icon: SvgPicture.asset('$kSvgDir/order/edit.svg'),
                // icon: Icon(Icons.edit, size: 18, color: Palette.primary),
                onPressed: () => _startEditing(item),
                // onPressed: () => _startEditing(item),
              ),
              IconButton(
                icon: SvgPicture.asset('$kSvgDir/order/delete.svg'),
                // icon: Icon(Icons.delete, size: 18, color: Palette.kE61010),
                onPressed: () {
                  ref
                      .read(advanceInvoiceControllerProvider.notifier)
                      .removeLineItem(item);
                },
              ),
            ],
          ),
          contentPadding: EdgeInsets.zero,
        );
      }).toList(),
    );
  }
}

class _ItemsOptionsSection extends ConsumerStatefulWidget {
  const _ItemsOptionsSection({super.key});
  @override
  ConsumerState<_ItemsOptionsSection> createState() =>
      _ServiceOptionsSectionState();
}

class _ServiceOptionsSectionState extends ConsumerState<_ItemsOptionsSection>
    with TickerProviderStateMixin {
  final List<TaxRate> selectedTaxes = [];
  final List<AdditionalCharge> selectedCharges = [];
  bool showAddOptions = false;
  bool showTaxForm = false;
  bool showChargeForm = false;
  bool showTaxDropdown = false;
  bool showChargeDropdown = false;
  String? editingTaxId;
  String? editingChargeId;
  final _taxNameController = TextEditingController();
  final _taxRateController = TextEditingController();
  final _chargeNameController = TextEditingController();
  final _chargeAmountController = TextEditingController();

  // Discount/Note state
  bool showDiscountForm = false;
  bool editingDiscount = false;
  DiscountType selectedType = DiscountType.fixed;
  final _discountValueController = TextEditingController();
  bool showNoteInput = false;
  final _noteController = TextEditingController();
  final _optionsFormController = ExpandableController();

  // Persistent controllers for each input
  final Map<String, TextEditingController> _taxControllers = {};
  final Map<String, TextEditingController> _chargeControllers = {};

  @override
  void dispose() {
    _taxNameController.dispose();
    _taxRateController.dispose();
    _chargeNameController.dispose();
    _chargeAmountController.dispose();
    _discountValueController.dispose();
    _noteController.dispose();
    for (var c in _taxControllers.values) {
      c.dispose();
    }
    for (var c in _chargeControllers.values) {
      c.dispose();
    }
    super.dispose();
  }

  bool _isValidTaxRate(String? value) {
    if (value == null || value.isEmpty) return false;
    final rate = double.tryParse(value.replaceAll(',', '').trim());
    if (rate == null) return false;
    return rate >= 0 && rate <= 100;
  }

  bool _isValidAmount(String? value) {
    if (value == null || value.isEmpty) return false;
    final amount = double.tryParse(value.replaceAll(',', '').trim());
    if (amount == null) return false;
    return amount >= 0;
  }

  double _calculateTaxAmount(TaxRate tax) {
    final lineItems = ref.read(createAdvanceInvoiceProvider).lineItems;
    final subtotal = lineItems.fold<double>(
        0, (sum, item) => sum + (item.quantity * item.unitPrice));
    return (subtotal * (tax.rate ?? 0)) / 100;
  }

  void _startEditingTax(TaxRate tax) {
    if (!_optionsFormController.isExpanded) {
      _optionsFormController.toggle();
    }

    setState(() {
      editingTaxId = tax.id;
      _taxNameController.text = tax.name;
      _taxRateController.text = tax.rate != null ? tax.rate.toString() : '';
      showTaxForm = true;
      showTaxDropdown = false;
      showChargeForm = false;
      showChargeDropdown = false;
    });
  }

  void _startEditingCharge(AdditionalCharge charge) {
    if (!_optionsFormController.isExpanded) {
      _optionsFormController.toggle();
    }

    setState(() {
      editingChargeId = charge.id;
      _chargeNameController.text = charge.name;
      _chargeAmountController.text =
          charge.amount != null ? charge.amount.toString() : '';
      showChargeForm = true;
      showChargeDropdown = false;
      showTaxForm = false;
      showTaxDropdown = false;
    });
  }

  void _startEditingDiscount() {
    if (!_optionsFormController.isExpanded) {
      _optionsFormController.toggle();
    }

    setState(() {
      showDiscountForm = !showDiscountForm;
      showNoteInput = false;
      showTaxDropdown = false;
      showChargeDropdown = false;
      showTaxForm = false;
      showChargeForm = false;
      // Set up discount form state
      final discount = ref.read(createAdvanceInvoiceProvider).discount;
      editingDiscount = discount != null;
      if (discount != null) {
        selectedType = discount.type;
        _discountValueController.text = discount.value.toString();
      } else {
        selectedType = DiscountType.fixed;
        _discountValueController.clear();
      }
    });
  }

  void _startEditingNote() {
    if (!_optionsFormController.isExpanded) {
      _optionsFormController.toggle();
    }

    setState(() {
      showNoteInput = !showNoteInput;
      showDiscountForm = false;
      showTaxDropdown = false;
      showChargeDropdown = false;
      showTaxForm = false;
      showChargeForm = false;
      // Set up note form state
      final note = ref.read(createAdvanceInvoiceProvider).note;
      _noteController.text = note ?? '';
    });
  }

  void _prepareTaxForm(TaxRate tax) {
    setState(() {
      _taxNameController.text = tax.name;
      _taxRateController.text = tax.rate != null ? tax.rate.toString() : '';
      showTaxForm = true;
      showTaxDropdown = false;
      showChargeForm = false;
      showChargeDropdown = false;
    });
  }

  void _prepareChargeForm(AdditionalCharge charge) {
    setState(() {
      _chargeNameController.text = charge.name;
      _chargeAmountController.text =
          charge.amount != null ? charge.amount.toString() : '';
      showChargeForm = true;
      showChargeDropdown = false;
      showTaxForm = false;
      showTaxDropdown = false;
    });
  }

  void _saveTax() {
    final name = _taxNameController.text.trim();
    final rate =
        double.tryParse(_taxRateController.text.replaceAll(',', '').trim()) ??
            0;

    if (name.isNotEmpty && _isValidTaxRate(_taxRateController.text)) {
      if (editingTaxId != null) {
        // Update existing tax
        final updatedTax = TaxRate(
          id: editingTaxId!,
          name: name,
          rate: rate,
          amount: 0,
        );
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .updateTax(updatedTax);
        setState(() {
          final index = selectedTaxes.indexWhere((t) => t.id == editingTaxId);
          if (index != -1) {
            selectedTaxes[index] = updatedTax;
          }
          editingTaxId = null;
          showTaxForm = false;
          showAddOptions = false;
          _taxNameController.clear();
          _taxRateController.clear();
        });
      } else {
        // Add new tax
        final newTax = TaxRate(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: name,
          rate: rate,
          amount: 0,
        );
        ref.read(advanceInvoiceControllerProvider.notifier).addTax(newTax);
        setState(() {
          selectedTaxes.add(newTax);
          showTaxForm = false;
          showAddOptions = false;
          _taxNameController.clear();
          _taxRateController.clear();
        });

        // add tax to the backend in the background
        createTask(newTax);
      }
    }
  }

  Future<void> createTask(TaxRate newTax) async {
    await ref.read(createTaxRateUseCaseProvider(newTax));
  }

  void _saveCharge() {
    final name = _chargeNameController.text.trim();
    final amount = double.tryParse(
            _chargeAmountController.text.replaceAll(',', '').trim()) ??
        0;

    if (name.isNotEmpty && _isValidAmount(_chargeAmountController.text)) {
      if (editingChargeId != null) {
        // Update existing charge
        final updatedCharge = AdditionalCharge(
          id: editingChargeId!,
          name: name,
          amount: amount,
        );
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .updateCharge(updatedCharge);
        setState(() {
          final index =
              selectedCharges.indexWhere((c) => c.id == editingChargeId);
          if (index != -1) {
            selectedCharges[index] = updatedCharge;
          }
          editingChargeId = null;
          showChargeForm = false;
          showAddOptions = false;
          _chargeNameController.clear();
          _chargeAmountController.clear();
        });
      } else {
        // Add new charge
        final newCharge = AdditionalCharge(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: name,
          amount: amount,
        );
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .addCharge(newCharge);
        setState(() {
          selectedCharges.add(newCharge);
          showChargeForm = false;
          showAddOptions = false;
          _chargeNameController.clear();
          _chargeAmountController.clear();
        });

        // add charge to the backend in the background
        createCharge(newCharge);
      }
    }
  }

  Future<void> createCharge(AdditionalCharge newCharge) async {
    await ref.read(createAdditionalChargeUseCaseProvider(newCharge));
  }

  void _cancelEdit() {
    setState(() {
      editingTaxId = null;
      editingChargeId = null;
      showTaxForm = false;
      showChargeForm = false;
      showAddOptions = false;
      _taxNameController.clear();
      _taxRateController.clear();
      _chargeNameController.clear();
      _chargeAmountController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    final settings =
        ref.watch(advanceInvoiceControllerProvider).settings.asData?.value;

    final textTheme = context.textTheme;
    final currencyCode = ref.read(currencyCodeProvider);

    // Filter out already selected taxes and charges
    final availableTaxes = (settings?.taxes ?? [])
        .where((tax) => !selectedTaxes.any((t) => t.name == tax.name))
        .toList();
    final availableCharges = (settings?.charges ?? [])
        .where((charge) => !selectedCharges.any((c) => c.name == charge.name))
        .toList();

    // Ensure persistent controllers for each tax/charge
    for (final tax in selectedTaxes) {
      _taxControllers.putIfAbsent(
          tax.id,
          () => TextEditingController(
              text: tax.rate != null ? tax.rate.toString() : ''));
    }
    for (final charge in selectedCharges) {
      _chargeControllers.putIfAbsent(
          charge.id,
          () => TextEditingController(
              text: charge.amount != null ? charge.amount.toString() : ''));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Items Options',
          style: textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 18,
            color: Palette.primaryBlack,
          ),
        ),
        const Gap(16),
        SmoothExpandable(
          controller: _optionsFormController,
          title: 'Add Items Option',
          initiallyExpanded: showAddOptions,
          onExpansionChanged: (expanded) {
            setState(() {
              showAddOptions = expanded;
              if (!showAddOptions) {
                showTaxForm = false;
                showChargeForm = false;
                showTaxDropdown = false;
                showChargeDropdown = false;
                showDiscountForm = false;
                showNoteInput = false;
              }
            });
          },
          content: _buildServiceOptionsContent(context, textTheme, currencyCode,
              availableTaxes, availableCharges),
        ),
        const Gap(16),
        // Display selected taxes and charges
        _buildSelectedItems(context, textTheme, currencyCode),
      ],
    );
  }

  Widget _buildServiceOptionsContent(
    BuildContext context,
    TextTheme textTheme,
    String currencyCode,
    List<TaxRate> availableTaxes,
    List<AdditionalCharge> availableCharges,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Service option buttons
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildServiceOptionButton(
              'Add Tax',
              'Add a tax to your invoice. Set the rate as a percentage (0 - 100%).',
              showTaxDropdown,
              () => setState(() {
                showTaxDropdown = !showTaxDropdown;
                showChargeDropdown = false;
                showDiscountForm = false;
                showNoteInput = false;
                showTaxForm = false;
                showChargeForm = false;
              }),
            ),
            _buildServiceOptionButton(
              'Add Charge',
              'Add a charge to your invoice eg Shipping fee, Processing fee, etc.',
              showChargeDropdown,
              () => setState(() {
                showChargeDropdown = !showChargeDropdown;
                showTaxDropdown = false;
                showDiscountForm = false;
                showNoteInput = false;
                showTaxForm = false;
                showChargeForm = false;
              }),
            ),
            _buildServiceOptionButton(
              'Add Discount',
              'Add a discount to your invoice. Choose fixed or percentage.',
              showDiscountForm,
              _startEditingDiscount,
            ),
            _buildServiceOptionButton(
              'Add Note',
              'Add a note to your invoice.',
              showNoteInput,
              _startEditingNote,
            ),
          ],
        ),
        const Gap(16),
        // Tax selection
        if (showTaxDropdown) _buildTaxSelection(availableTaxes),
        // Charge selection
        if (showChargeDropdown) _buildChargeSelection(availableCharges),
        // Forms
        if (showTaxForm) _buildTaxForm(textTheme),
        if (showChargeForm) _buildChargeForm(textTheme, currencyCode),
        if (showDiscountForm) _buildDiscountForm(textTheme, currencyCode),
        if (showNoteInput) _buildNoteForm(textTheme),
      ],
    );
  }

  Widget _buildServiceOptionButton(
      String title, String tooltip, bool showingForm, VoidCallback onPressed) {
    return Tooltip(
      message: tooltip,
      child: OutlinedButton.icon(
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          minimumSize: const Size(0, 40),
          side: BorderSide(color: Palette.stroke),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        onPressed: onPressed,
        icon: Icon(showingForm ? Icons.remove : Icons.add,
            size: 16, color: Palette.primary),
        label: Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Palette.primary,
          ),
        ),
      ),
    );
  }

  Widget _buildTaxSelection(List<TaxRate> availableTaxes) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Palette.kF7F7F7,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Palette.stroke),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Tax',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Palette.primaryBlack,
                ),
          ),
          const Gap(12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              ...availableTaxes.map((tax) => ActionChip(
                    backgroundColor: Colors.white,
                    side: BorderSide(color: Palette.stroke),
                    label: Text(
                      tax.name,
                      style: const TextStyle(fontSize: 13),
                    ),
                    onPressed: () => _prepareTaxForm(tax),
                  )),
              ActionChip(
                backgroundColor: Palette.primary.withValues(alpha: 0.1),
                side: BorderSide(color: Palette.primary),
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.add, size: 16, color: Palette.primary),
                    const Gap(4),
                    Text(
                      'Add new tax',
                      style: TextStyle(
                        fontSize: 13,
                        color: Palette.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                onPressed: () {
                  setState(() {
                    showTaxForm = true;
                    showTaxDropdown = false;
                    _taxNameController.clear();
                    _taxRateController.clear();
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChargeSelection(List<AdditionalCharge> availableCharges) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Palette.kF7F7F7,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Palette.stroke),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Charge',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Palette.primaryBlack,
                ),
          ),
          const Gap(12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              ...availableCharges.map((charge) => ActionChip(
                    backgroundColor: Colors.white,
                    side: BorderSide(color: Palette.stroke),
                    label: Text(
                      charge.name,
                      style: const TextStyle(fontSize: 13),
                    ),
                    onPressed: () => _prepareChargeForm(charge),
                  )),
              ActionChip(
                backgroundColor: Palette.primary.withValues(alpha: 0.1),
                side: BorderSide(color: Palette.primary),
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.add, size: 16, color: Palette.primary),
                    const Gap(4),
                    Text(
                      'Add new charge',
                      style: TextStyle(
                        fontSize: 13,
                        color: Palette.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                onPressed: () {
                  setState(() {
                    showChargeForm = true;
                    showChargeDropdown = false;
                    _chargeNameController.clear();
                    _chargeAmountController.clear();
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTaxForm(TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Palette.stroke),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            editingTaxId != null ? 'Edit Tax' : 'Add New Tax',
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Palette.primaryBlack,
            ),
          ),
          const Gap(16),
          Row(
            children: [
              Expanded(
                child: CompactInput(
                  controller: _taxNameController,
                  hint: 'Tax Name',
                  autofocus: _taxNameController.text.isEmpty,
                ),
              ),
              const Gap(12),
              Expanded(
                child: CompactInput(
                  controller: _taxRateController,
                  hint: 'Rate (0 - 100%)',
                  suffix: '%',
                  keyboardType: TextInputType.number,
                  inputFormatters: [Validators.formatRange(0, 100)],
                  autofocus: _taxNameController.text.isNotEmpty,
                  onEditingComplete: _saveTax,
                ),
              ),
            ],
          ),
          const Gap(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                style: TextButton.styleFrom(
                  minimumSize: const Size(0, 36),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                onPressed: _cancelEdit,
                child: const Text('Cancel'),
              ),
              const Gap(8),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size(0, 36),
                  backgroundColor: Palette.primary,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                onPressed: _saveTax,
                child: Text(editingTaxId != null ? 'Update' : 'Save'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChargeForm(TextTheme textTheme, String currencyCode) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Palette.stroke),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            editingChargeId != null ? 'Edit Charge' : 'Add New Charge',
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Palette.primaryBlack,
            ),
          ),
          const Gap(16),
          Row(
            children: [
              Expanded(
                child: CompactInput(
                  controller: _chargeNameController,
                  hint: 'Charge Name',
                  autofocus: _chargeNameController.text.isEmpty,
                ),
              ),
              const Gap(12),
              Expanded(
                child: CompactInput(
                  controller: _chargeAmountController,
                  hint: 'Amount',
                  prefix: '${CurrencyWidget.symbol(context, currencyCode)} ',
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    Validators.formatCurrency(decimalPlaces: 2)
                  ],
                  autofocus: _chargeNameController.text.isNotEmpty,
                  onEditingComplete: _saveCharge,
                ),
              ),
            ],
          ),
          const Gap(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                style: TextButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                onPressed: _cancelEdit,
                child: const Text('Cancel'),
              ),
              const Gap(8),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Palette.primary,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                onPressed: _saveCharge,
                child: Text(editingChargeId != null ? 'Update' : 'Save'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDiscountForm(TextTheme textTheme, String currencyCode) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Palette.stroke),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            editingDiscount ? 'Edit Discount' : 'Add Discount',
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Palette.primaryBlack,
            ),
          ),
          const Gap(16),
          Row(
            children: [
              Expanded(
                child: AdvancedDropdown<DiscountType>(
                  selectedValue: selectedType,
                  options: DiscountType.values,
                  hint: 'Type',
                  itemToString: (type) =>
                      type == DiscountType.fixed ? 'Fixed' : 'Percentage',
                  onChanged: (type) {
                    if (type != null) {
                      setState(() {
                        selectedType = type;
                        _discountValueController.clear();
                      });
                    }
                  },
                  searchable: false,
                  menuMaxHeight: 120,
                  height: 34,
                ),
              ),
              const Gap(12),
              Expanded(
                child: CompactInput(
                  controller: _discountValueController,
                  hint: selectedType == DiscountType.fixed
                      ? 'Amount'
                      : 'Value (%)',
                  prefix: selectedType == DiscountType.fixed
                      ? '${CurrencyWidget.symbol(context, currencyCode)} '
                      : null,
                  suffix: selectedType == DiscountType.percentage ? '%' : null,
                  keyboardType: TextInputType.number,
                  inputFormatters: selectedType == DiscountType.percentage
                      ? [Validators.formatRange(0, 100)]
                      : [Validators.formatCurrency(decimalPlaces: 2)],
                  onEditingComplete: _saveDiscount,
                ),
              ),
            ],
          ),
          const Gap(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                style: TextButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                onPressed: () {
                  setState(() {
                    showDiscountForm = false;
                    editingDiscount = false;
                    _discountValueController.clear();
                  });
                },
                child: const Text('Cancel'),
              ),
              const Gap(8),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Palette.primary,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                onPressed: _saveDiscount,
                child: Text(editingDiscount ? 'Update' : 'Save'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNoteForm(TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Palette.stroke),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            ref.read(createAdvanceInvoiceProvider).note != null
                ? 'Edit Note'
                : 'Add Note',
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Palette.primaryBlack,
            ),
          ),
          const Gap(16),
          CompactInput(
            controller: _noteController,
            hint: 'Enter note...',
            autofocus: true,
            height: 34,
          ),
          const Gap(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                style: TextButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                onPressed: () {
                  setState(() {
                    showNoteInput = false;
                    _noteController.clear();
                  });
                },
                child: const Text('Cancel'),
              ),
              const Gap(8),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Palette.primary,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                onPressed: _saveNote,
                child: Text(
                  ref.read(createAdvanceInvoiceProvider).note != null
                      ? 'Update'
                      : 'Save',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedItems(
      BuildContext context, TextTheme textTheme, String currencyCode) {
    final selectedTaxes = ref.watch(createAdvanceInvoiceProvider).taxes ?? [];
    final selectedCharges =
        ref.watch(createAdvanceInvoiceProvider).charges ?? [];
    final params = ref.watch(createAdvanceInvoiceProvider);

    if (selectedTaxes.isEmpty &&
        selectedCharges.isEmpty &&
        (params.discount == null) &&
        (params.note == null || params.note!.isEmpty)) {
      return Center(
        child: Text(
          'No service options added yet',
          style: textTheme.bodyMedium?.copyWith(
            color: Palette.placeholder,
            fontStyle: FontStyle.italic,
          ),
        ),
      );
    }

    return Column(
      children: [
        // Selected Taxes
        ...selectedTaxes.map((tax) => ListTile(
              dense: true,
              title: Text(
                tax.name,
                style: textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Palette.primaryBlack,
                ),
              ),
              subtitle: Text(
                'Rate: ${tax.rate}%',
                style: textTheme.bodySmall?.copyWith(
                  color: Palette.blackSecondary,
                ),
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Consumer(
                    builder: (context, ref, _) {
                      ref.watch(createAdvanceInvoiceProvider);
                      return Text(
                        CurrencyWidget.value(
                            context, currencyCode, _calculateTaxAmount(tax)),
                        style: textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Palette.primaryBlack,
                        ),
                      );
                    },
                  ),
                  const Gap(8),
                  IconButton(
                    icon: SvgPicture.asset('$kSvgDir/order/edit.svg'),
                    // icon: Icon(Icons.edit, size: 18, color: Palette.primary),
                    onPressed: () => _startEditingTax(tax),
                  ),
                  IconButton(
                    icon: SvgPicture.asset('$kSvgDir/order/delete.svg'),
                    // icon: Icon(Icons.delete, size: 18, color: Palette.kE61010),
                    onPressed: () {
                      ref
                          .read(advanceInvoiceControllerProvider.notifier)
                          .removeTax(tax);
                    },
                  ),
                ],
              ),
              contentPadding: EdgeInsets.zero,
            )),
        // Selected Charges
        ...selectedCharges.map((charge) => ListTile(
              dense: true,
              title: Text(
                charge.name,
                style: textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Palette.primaryBlack,
                ),
              ),
              subtitle: Text(
                'Amount: ${CurrencyWidget.value(context, currencyCode, charge.amount ?? 0)}',
                style: textTheme.bodySmall?.copyWith(
                  color: Palette.blackSecondary,
                ),
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    CurrencyWidget.value(
                        context, currencyCode, charge.amount ?? 0),
                    style: textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Palette.primaryBlack,
                    ),
                  ),
                  const Gap(8),
                  IconButton(
                    icon: SvgPicture.asset('$kSvgDir/order/edit.svg'),
                    // icon: Icon(Icons.edit, size: 18, color: Palette.primary),
                    onPressed: () => _startEditingCharge(charge),
                  ),
                  IconButton(
                    icon: SvgPicture.asset('$kSvgDir/order/delete.svg'),
                    // icon: Icon(Icons.delete, size: 18, color: Palette.kE61010),
                    onPressed: () {
                      ref
                          .read(advanceInvoiceControllerProvider.notifier)
                          .removeCharge(charge);
                    },
                  ),
                ],
              ),
              contentPadding: EdgeInsets.zero,
            )),
        // Selected Discount
        if (params.discount != null)
          ListTile(
            dense: true,
            title: Text(
              'Discount',
              style: textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Palette.primaryBlack,
              ),
            ),
            subtitle: Text(
              'Type: ${params.discount!.type.name}',
              style: textTheme.bodySmall?.copyWith(
                color: Palette.blackSecondary,
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Palette.blackSecondary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    params.discount!.type == DiscountType.fixed
                        ? CurrencyWidget.value(
                            context, currencyCode, params.discount!.value)
                        : '${params.discount!.value}%',
                    style: textTheme.bodySmall?.copyWith(
                      color: Palette.blackSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Gap(8),
                IconButton(
                  icon: SvgPicture.asset('$kSvgDir/order/edit.svg'),
                  // icon: Icon(Icons.edit, size: 18, color: Palette.primary),
                  onPressed: _startEditingDiscount,
                ),
                IconButton(
                  icon: SvgPicture.asset('$kSvgDir/order/delete.svg'),
                  // icon: Icon(Icons.delete, size: 18, color: Palette.kE61010),
                  onPressed: () {
                    ref
                        .read(advanceInvoiceControllerProvider.notifier)
                        .removeDiscount();
                  },
                ),
              ],
            ),
            contentPadding: EdgeInsets.zero,
          ),
        // Selected Note
        if (params.note != null && params.note!.isNotEmpty)
          ListTile(
            dense: true,
            title: Text(
              'Note',
              style: textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Palette.primaryBlack,
              ),
            ),
            subtitle: Text(
              params.note!,
              style: textTheme.bodySmall?.copyWith(
                color: Palette.blackSecondary,
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: SvgPicture.asset('$kSvgDir/order/edit.svg'),
                  // icon: Icon(Icons.edit, size: 18, color: Palette.primary),
                  onPressed: _startEditingNote,
                ),
                IconButton(
                  icon: SvgPicture.asset('$kSvgDir/order/delete.svg'),
                  // icon: Icon(Icons.delete, size: 18, color: Palette.kE61010),
                  onPressed: () {
                    ref
                        .read(advanceInvoiceControllerProvider.notifier)
                        .removeNote();
                  },
                ),
              ],
            ),
            isThreeLine: true,
            contentPadding: EdgeInsets.zero,
          ),
      ],
    );
  }

  void _saveDiscount() {
    final value = num.tryParse(
            _discountValueController.text.replaceAll(',', '').trim()) ??
        0;
    if (value > 0 ||
        (selectedType == DiscountType.percentage &&
            value >= 0 &&
            value <= 100)) {
      final newDiscount = Discount(type: selectedType, value: value);
      if (editingDiscount) {
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .updateDiscount(newDiscount);
      } else {
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .addDiscount(newDiscount);
      }
      setState(() {
        showDiscountForm = false;
        editingDiscount = false;
        _discountValueController.clear();
      });
    }
  }

  void _saveNote() {
    final value = _noteController.text.trim();
    if (value.isNotEmpty) {
      final note = ref.read(createAdvanceInvoiceProvider).note;
      if (note != null) {
        ref.read(advanceInvoiceControllerProvider.notifier).updateNote(value);
      } else {
        ref.read(advanceInvoiceControllerProvider.notifier).addNote(value);
      }
      setState(() {
        showNoteInput = false;
        _noteController.clear();
      });
    }
  }
}

class _SummarySection extends ConsumerWidget {
  const _SummarySection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = context.textTheme;
    final currencyCode = ref.read(currencyCodeProvider);
    final createParams = ref.watch(createAdvanceInvoiceProvider);
    final lineItems = createParams.lineItems ?? [];
    final taxes = createParams.taxes ?? [];
    final charges = createParams.charges ?? [];
    final discount = createParams.discount;

    // Calculate subtotal
    final subtotal = lineItems.fold<double>(
      0,
      (sum, item) => sum + (item.quantity * item.unitPrice),
    );

    // Calculate tax amount
    final taxAmount = taxes.fold<double>(0, (sum, tax) {
      final taxRate = tax.rate ?? 0;
      return sum + (subtotal * (taxRate / 100));
    });

    // Calculate charge amount
    final chargeAmount = charges.fold<double>(0, (sum, charge) {
      return sum + (charge.amount?.toDouble() ?? 0);
    });

    // Calculate discount amount
    double discountAmount = 0;
    if (discount != null) {
      if (discount.type == DiscountType.fixed) {
        discountAmount = discount.value.toDouble();
      } else {
        discountAmount = (subtotal * discount.value.toDouble()) / 100;
      }
    }

    // Calculate total
    final total = subtotal + taxAmount + chargeAmount - discountAmount;

    // Don't show summary if no items
    if (lineItems.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: Colors.white,
      elevation: 0,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Palette.stroke),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Invoice Summary',
                style: textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                  color: Palette.primaryBlack,
                ),
              ),
              const Gap(20),
              Row(
                children: [
                  Expanded(child: Container()),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        _buildSummaryRow(
                          context,
                          'Subtotal',
                          subtotal,
                          currencyCode,
                          textTheme,
                        ),
                        if (taxes.isNotEmpty) ...[
                          const Gap(8),
                          ...taxes.map((tax) => Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: _buildSummaryRow(
                                  context,
                                  '${tax.name} (${tax.rate}%)',
                                  (subtotal * ((tax.rate ?? 0) / 100)),
                                  currencyCode,
                                  textTheme,
                                ),
                              )),
                        ],
                        if (charges.isNotEmpty) ...[
                          const Gap(8),
                          ...charges.map((charge) => Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: _buildSummaryRow(
                                  context,
                                  charge.name,
                                  (charge.amount ?? 0).toDouble(),
                                  currencyCode,
                                  textTheme,
                                ),
                              )),
                        ],
                        if (discountAmount > 0) ...[
                          const Gap(8),
                          _buildSummaryRow(
                            context,
                            'Discount',
                            -discountAmount,
                            currencyCode,
                            textTheme,
                            isDiscount: true,
                          ),
                        ],
                        const Gap(12),
                        Container(
                          height: 1,
                          color: Palette.stroke,
                        ),
                        const Gap(12),
                        _buildSummaryRow(
                          context,
                          'Total',
                          total,
                          currencyCode,
                          textTheme,
                          isTotal: true,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryRow(
    BuildContext context,
    String label,
    double value,
    String currencyCode,
    TextTheme textTheme, {
    bool isTotal = false,
    bool isDiscount = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            label,
            style: textTheme.bodyMedium?.copyWith(
              color: isTotal ? Palette.primaryBlack : Palette.blackSecondary,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const Gap(16),
        Text(
          CurrencyWidget.value(context, currencyCode, value.abs()),
          style: textTheme.bodyMedium?.copyWith(
            color: isDiscount
                ? Palette.kE61010
                : isTotal
                    ? Palette.primaryBlack
                    : Palette.blackSecondary,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
          ),
          textAlign: TextAlign.right,
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/upload_pod_params.dart';
import 'package:td_procurement/app/advance_invoice/presentation/controllers/advance_invoice_controller.dart';
import 'package:td_procurement/app/business_verification/presentation/controllers/file_upload.dart';
import 'package:td_procurement/app/business_verification/presentation/widgets/upload_documents.dart';

class UploadPodModal extends ConsumerStatefulWidget {
  final String invoiceId;
  const UploadPodModal({super.key, required this.invoiceId});

  @override
  ConsumerState<UploadPodModal> createState() => _UploadPodModalState();
}

class _UploadPodModalState extends ConsumerState<UploadPodModal> {
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    final fileUploadController = ref.watch(fileUploadProvider.notifier);
    final fileUploadState = ref.watch(fileUploadProvider);
    final advanceInvoiceController =
        ref.watch(advanceInvoiceControllerProvider.notifier);

    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('upload', style: TextStyle(fontSize: 20)),
          const SizedBox(height: 20),
          UploadDocuments(
            onFilePicked: (files) {
              fileUploadController.addFiles(files);
            },
          ),
          const SizedBox(height: 20),
          TdTextButton(
            onPressed: () {
              if (_formKey.currentState!.validate()) {
                final params = UploadPodParams(
                  invoiceId: widget.invoiceId,
                  documents: fileUploadState.files,
                );
                advanceInvoiceController.uploadPod(params);
                Navigator.of(context).pop();
              }
            },
            text: 'upload',
          ),
        ],
      ),
    );
  }
}

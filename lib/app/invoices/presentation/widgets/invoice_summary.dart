import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/app/invoices/domain/entities/invoices_params.dart';
import 'package:td_procurement/app/invoices/domain/entities/payout_params.dart';
import 'package:td_procurement/app/invoices/domain/use_cases/invoices_use_cases.dart';
import 'package:td_procurement/app/invoices/presentation/widgets/invoice_status_chip.dart';
import 'package:td_procurement/app/invoices/presentation/widgets/invoice_summary_table.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class InvoiceSummary extends ConsumerStatefulWidget {
  final String? orderId;
  final InvoiceSummaryArgs? args;
  final bool isCloseIcon;
  final String invoiceType;

  const InvoiceSummary(this.orderId,
      {super.key, this.args, this.isCloseIcon = false, this.invoiceType = ''});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _InvoiceSummary();
  }
}

class _InvoiceSummary extends ConsumerState<InvoiceSummary> {
  final isFetchingInvoicePdf = ValueNotifier<bool>(false);
  late final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
  AsyncValue<InvoiceSummaryArgs> invoiceArgs = const AsyncValue.loading();

  @override
  void initState() {
    if (widget.args != null) {
      invoiceArgs = AsyncValue.data(widget.args!);
    } else {
      fetchInvoice();
    }
    super.initState();
  }

  @override
  dispose() {
    isFetchingInvoicePdf.dispose();
    super.dispose();
  }

  Future<void> fetchInvoice() async {
    final outletId = ref.read(outletIdProvider);

    if (widget.orderId == null ||
        widget.orderId!.isEmpty ||
        outletId == null ||
        outletId.isEmpty) {
      if (mounted) {
        setState(() {
          invoiceArgs = AsyncValue.error(
              'Invalid order or outlet ID', StackTrace.current);
        });
      }
      return;
    }

    try {
      if (mounted) {
        setState(() {
          invoiceArgs = const AsyncValue.loading();
        });
      } else {
        invoiceArgs = const AsyncValue.loading();
      }

      final res = await ref.read(invoiceSummaryUseCaseProvider(
          InvoiceDetailParams(
              orderId: widget.orderId!,
              outletId: outletId,
              invoiceType: widget.invoiceType)));

      if (mounted) {
        setState(() {
          res.when(
            success: (result) {
              invoiceArgs = AsyncValue.data(InvoiceSummaryArgs(
                invoice: result.invoice,
                type: InvoiceType.purchase,
                isDownloadInvoice: false,
                payout: false,
                invoiceUrl: result.invoiceUrl,
              ));
            },
            failure: (error, _) {
              invoiceArgs = AsyncValue.error(error, StackTrace.current);
            },
          );
        });
      }
    } catch (error, stackTrace) {
      if (mounted) {
        setState(() {
          invoiceArgs = AsyncValue.error(error, stackTrace);
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return invoiceArgs.when(
      data: (args) => _dataWidget(args),
      error: (error, stackTrace) =>
          FailureWidget(e: error, retry: fetchInvoice),
      loading: () => Skeletonizer(
        enabled: true,
        child: _dataWidget(InvoiceSummaryArgs.defaultValue()),
      ),
    );
  }

  Widget _dataWidget(InvoiceSummaryArgs args) {
    final textTheme = Theme.of(context).textTheme;
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 70,
            width: double.infinity,
            child: DecoratedBox(
              decoration: BoxDecoration(color: Palette.kFCFCFC),
              child: Row(
                children: [
                  const Gap(10),
                  Skeleton.shade(
                    child: IconButton(
                      icon: SvgPicture.asset(
                          widget.isCloseIcon ? kCloseSvg : kSvgArrowBackIcon),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                  const Gap(10),
                  Text(
                    '${widget.invoiceType} Invoice Summary',
                    style:
                        textTheme.bodyLarge?.copyWith(color: Palette.k6B797C),
                  ),
                  const Spacer(),
                  FilledButton(
                    onPressed: () {
                      if (args.payout) {
                        handlePayoutInvoiceDownload(args.invoice);
                      } else {
                        handleInvoiceDownload(args);
                      }
                    },
                    style: FilledButton.styleFrom(
                      fixedSize: const Size(80, 32),
                      minimumSize: const Size(80, 32),
                      backgroundColor: Palette.primary,
                    ),
                    child: ValueListenableBuilder<bool>(
                      valueListenable: isFetchingInvoicePdf,
                      builder: (context, state, _) => state
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(),
                            )
                          : const Text('Print'),
                    ),
                  ),
                  const Gap(30)
                ],
              ),
            ),
          ),
          const Gap(20),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 30),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Invoice #${args.invoice.invoiceNumber}',
                  style: textTheme.headlineMedium,
                ),
                const Gap(10),
                Row(
                  children: [
                    if (args.invoice.status != InvoiceStatus.paid) ...[
                      Text(
                        'Due ${args.invoice.dueAt.toDate()}',
                        style: textTheme.bodyMedium
                            ?.copyWith(color: Palette.blackSecondary),
                      ),
                      const Gap(10),
                    ],
                    InvoiceStatusChip(status: args.invoice.status)
                  ],
                ),
                const Gap(20),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 1,
                      child: Text(
                        'Deliver To',
                        style: textTheme.bodyMedium?.copyWith(
                          color: Palette.blackSecondary,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 6,
                      child: Text(
                        '${args.invoice.outletBusinessName}, ${args.invoice.address.address1 ?? ''}',
                        style: textTheme.bodyMedium
                            ?.copyWith(fontWeight: FontWeight.w500),
                      ),
                    ),
                  ],
                ),
                const Gap(50),
                InvoiceSummaryTable(invoice: args.invoice),
                const Gap(100),
                if (args.invoice.status != InvoiceStatus.paid &&
                    args.invoice.bankAccount?.bankName != null &&
                    args.invoice.bankAccount?.accountNumber != null)
                  Container(
                    decoration: BoxDecoration(
                        border: Border.all(color: Palette.kE7E7E7),
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: const [
                          BoxShadow(
                              offset: Offset(0, 4),
                              blurRadius: 4,
                              spreadRadius: 0,
                              color: Palette.k0000000A)
                        ]),
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'How to pay',
                          style: textTheme.bodyLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: Palette.primary),
                        ),
                        const Gap(10),
                        Text.rich(
                          TextSpan(
                            text:
                                'Pay ${CurrencyWidget.value(context, args.invoice.currency.iso, args.invoice.amount)}',
                            children: [
                              TextSpan(
                                text: ' with bank transfer',
                                style: textTheme.bodyLarge?.copyWith(
                                    fontWeight: FontWeight.w500, fontSize: 18),
                              )
                            ],
                          ),
                          style: textTheme.headlineSmall,
                        ),
                        Text(
                          'Transfer funds to the following bank account to complete payment for this invoice',
                          style: textTheme.bodyMedium?.copyWith(
                            color: Palette.blackSecondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Divider(
                          height: 50,
                          thickness: 1,
                          color: Palette.kE7E7E7,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Account Number',
                              style: textTheme.bodyMedium
                                  ?.copyWith(fontWeight: FontWeight.w600),
                            ),
                            Text(
                              args.invoice.bankAccount!.accountNumber ?? '',
                              style: textTheme.bodyMedium
                                  ?.copyWith(fontWeight: FontWeight.w700),
                            ),
                          ],
                        ),
                        const Gap(10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Bank Name',
                              style: textTheme.bodyMedium
                                  ?.copyWith(fontWeight: FontWeight.w600),
                            ),
                            Text(
                              args.invoice.bankAccount!.bankName ?? '',
                              style: textTheme.bodyMedium
                                  ?.copyWith(fontWeight: FontWeight.w700),
                            ),
                          ],
                        )
                      ],
                    ),
                  )
              ],
            ),
          ),
        ],
      ),
    );
  }

  handleInvoiceDownload(InvoiceSummaryArgs args) async {
    isFetchingInvoicePdf.value = true;
    // final response = await ref.read(switch (args.type) {
    //   InvoiceType.purchase => downloadInvoiceUseCaseProvider(
    //       args.invoice.orders == null ? '' : args.invoice.orders!.first.id!),
    //   InvoiceType.sales => downloadSalesInvoiceUseCaseProvider(
    //       args.invoice.orders == null ? '' : args.invoice.orders!.first.id!),
    // });
    if (mounted) {
      isFetchingInvoicePdf.value = false;
      openUri(args.invoiceUrl);
      // response.when(
      //   success: (url) =>
      //       openUrl(url, context), // Enables platform-specific URL handling.
      //   failure: (err, _) => Toast.apiError(err, context),
      // );
    }
  }

  handlePayoutInvoiceDownload(Invoice? invoice) async {
    if (invoice == null) return;
    isFetchingInvoicePdf.value = true;
    final response = await ref.read(downloadPayoutInvoiceUseCaseProvider(
        PayoutInvoiceParams(
            invoice.orders!.first.id!, invoice.retailOutletId!)));
    if (mounted) {
      isFetchingInvoicePdf.value = false;
      response.when(
        success: (url) => openUri(url),
        failure: (err, _) => Toast.apiError(err, context),
      );
    }
  }
}

class InvoiceSummaryArgs {
  final Invoice invoice;
  final InvoiceType type;
  final bool isDownloadInvoice;
  final bool payout;
  final String invoiceUrl;

  InvoiceSummaryArgs({
    required this.invoice,
    this.type = InvoiceType.sales,
    this.isDownloadInvoice = false,
    this.payout = false,
    this.invoiceUrl = '',
  });

  factory InvoiceSummaryArgs.defaultValue() => InvoiceSummaryArgs(
        invoice: Invoice.loadValue(),
        type: InvoiceType.purchase,
        isDownloadInvoice: false,
        payout: false,
        invoiceUrl: '',
      );

  InvoiceSummaryArgs copyWith({
    Invoice? invoice,
    InvoiceType? type,
    bool? isDownloadInvoice,
    bool? payout,
    String? invoiceUrl,
  }) {
    return InvoiceSummaryArgs(
      invoice: invoice ?? this.invoice,
      type: type ?? this.type,
      isDownloadInvoice: isDownloadInvoice ?? this.isDownloadInvoice,
      payout: payout ?? this.payout,
      invoiceUrl: invoiceUrl ?? this.invoiceUrl,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'invoice': invoice.toJson(),
      'type': type.name,
      'isDownloadInvoice': isDownloadInvoice,
      'payout': payout,
      'invoiceUrl': invoiceUrl,
    };
  }

  factory InvoiceSummaryArgs.fromMap(Map<String, dynamic> map) {
    return InvoiceSummaryArgs(
      invoice: Invoice.fromJson(map['invoice']),
      type: map['type'].toString().contains('sales')
          ? InvoiceType.sales
          : InvoiceType.purchase,
      isDownloadInvoice: map['isDownloadInvoice'] ?? false,
      payout: map['payout'] ?? false,
      invoiceUrl: map['invoiceUrl'] ?? '',
    );
  }

  factory InvoiceSummaryArgs.fromJson(String source) =>
      InvoiceSummaryArgs.fromMap(json.decode(source));

  factory InvoiceSummaryArgs.fromQueryParams(Map<String, String> params) {
    return InvoiceSummaryArgs(
      invoice: Invoice.fromJson(json.decode(params['invoice'] ?? '{}')),
      type:
          params['type'] == 'sales' ? InvoiceType.sales : InvoiceType.purchase,
      isDownloadInvoice: params['isDownloadInvoice'] == 'true',
      payout: params['payout'] == 'true',
      invoiceUrl: params['invoiceUrl'] ?? '',
    );
  }

  @override
  String toString() {
    return 'InvoiceSummaryArgs(invoice: $invoice, type: $type, isDownloadInvoice: $isDownloadInvoice, payout: $payout, invoiceUrl: $invoiceUrl)';
  }
}

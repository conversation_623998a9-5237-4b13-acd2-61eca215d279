import 'package:flutter/material.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class SmoothExpandable extends StatefulWidget {
  const SmoothExpandable({
    super.key,
    required this.title,
    required this.content,
    this.initiallyExpanded = false,
    this.onExpansionChanged,
    this.titleStyle,
    this.buttonStyle,
    this.leading,
    this.controller,
  });

  final String title;
  final Widget content;
  final bool initiallyExpanded;
  final ValueChanged<bool>? onExpansionChanged;
  final TextStyle? titleStyle;
  final ButtonStyle? buttonStyle;
  final Widget? leading;
  final ExpandableController? controller;

  @override
  State<SmoothExpandable> createState() => _SmoothExpandableState();
}

class _SmoothExpandableState extends State<SmoothExpandable>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _heightFactor;
  late Animation<double> _iconRotation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;

    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _heightFactor = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    _iconRotation = Tween<double>(
      begin: 0.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (_isExpanded) {
      _controller.value = 1.0;
    }

    if (widget.controller != null) {
      widget.controller!._setToggle(_toggleExpansion);
      widget.controller!._updateExpanded(_isExpanded);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }

      widget.controller?._updateExpanded(_isExpanded);
    });
    widget.onExpansionChanged?.call(_isExpanded);
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            if (widget.leading != null) ...[
              Expanded(child: widget.leading!),
              const SizedBox(width: 8),
            ],
            TextButton.icon(
              style: widget.buttonStyle ??
                  TextButton.styleFrom(
                    foregroundColor: Palette.primary,
                    textStyle: const TextStyle(fontWeight: FontWeight.w600),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
              onPressed: _toggleExpansion,
              icon: AnimatedBuilder(
                animation: _iconRotation,
                builder: (context, child) {
                  return Transform.rotate(
                    angle: _iconRotation.value * 3.14159 * 2,
                    child: Icon(
                      _isExpanded ? Icons.remove : Icons.add,
                      size: 18,
                      color: Palette.primary,
                    ),
                  );
                },
              ),
              label: Text(
                widget.title,
                style: widget.titleStyle ??
                    textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Palette.primary,
                    ),
              ),
            ),
          ],
        ),
        AnimatedBuilder(
          animation: _heightFactor,
          builder: (context, child) {
            return ClipRect(
              child: Align(
                heightFactor: _heightFactor.value,
                child: child,
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.only(top: 8.0, left: 2.0),
            child: widget.content,
          ),
        ),
      ],
    );
  }
}

class ExpandableController {
  final ValueNotifier<bool> _isExpandedNotifier = ValueNotifier(false);
  VoidCallback? _toggleFunction;

  /// Returns `true` if the expandable is currently expanded
  bool get isExpanded => _isExpandedNotifier.value;

  /// Toggles the expandable
  void toggle() => _toggleFunction?.call();

  void _setToggle(VoidCallback toggle) {
    _toggleFunction = toggle;
  }

  void _updateExpanded(bool value) {
    _isExpandedNotifier.value = value;
  }

  void dispose() {
    _isExpandedNotifier.dispose();
  }
}

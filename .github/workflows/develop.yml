name: Continuous Integration & Deployment

on:
  pull_request:
    branches:
      - develop
  push:
    branches:
      - develop
jobs:
  build_trade_depot_web:
    name: Build Procurement Flutter Home Page
    runs-on: ubuntu-latest
    environment:
      name: dev
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          flutter-version: '3.32.0'
      - name: Run dart env
        run: dart tool/env.dart
        env:
          INTERCOM_APP_ID: b8hcgyaw
          FLUTTER_APP_FLAVOR: dev
          FIREBASE_SERVICE_URL: 'https://us-central1-tradedepot-retail-dev2.cloudfunctions.net'
          CONSOLE_URL: "https://console.sandbox.tradedepot.co"
          APP_URL: "https://app.sandbox.tradedepot.co"
          AWS_API_URL: "https://r5zbm6nq76.execute-api.us-east-1.amazonaws.com/dev"
          AWS_API_URL_V2: "https://irwwkqfy8d.execute-api.us-east-1.amazonaws.com/dev"
          AWS_API_URL_V3: "https://1esgvy3ozj.execute-api.us-east-1.amazonaws.com/dev"
          AWS_API_URL_V4: "https://0zl4k1w352.execute-api.us-east-1.amazonaws.com/dev"
          SEARCH_URL: "https://dqspuadfb2.execute-api.us-east-1.amazonaws.com/dev"
          SENTRY_DSN: "https://<EMAIL>/4508262252412928"
          GOOGLE_API_KEY: ${{ secrets.GOOGLE_MAP_KEY }}
          HUBSPOT_API_KEY: ${{ secrets.HUBSPOT_API_KEY }}
      - name: Run Web env
        run: . ./parse-tmp-env.sh ./web/index.example.html ./web/index.html
        env:
          GOOGLE_API_KEY: ${{ secrets.GOOGLE_MAP_KEY }}
          INTERCOM_APP_ID: b8hcgyaw
      # - run: flutter upgrade
      - run: git config --global url."https://${{ secrets.GIT_USER }}:${{ secrets.GIT_TOKEN }}@github.com/".insteadOf https://github.com/
      - run: flutter pub get
      - run: flutter test
      - run: flutter config --enable-web
      - run: flutter build web --release --source-maps #--web-renderer canvaskit
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_DEFAULT_REGION }}
      - name: Deploy static site to S3 bucket
        env:
          S3_BUCKET_NAME: td-flutter-procurement
          S3_CACHE_MAX_AGE: ${{ secrets.S3_CACHE_MAX_AGE }}
        run: |
          aws s3 sync build/web s3://$S3_BUCKET_NAME --delete
          aws s3 cp s3://$S3_BUCKET_NAME/ s3://$S3_BUCKET_NAME/ --metadata-directive REPLACE \
          --exclude "*" --include "*.jpg" --include "*.gif" --include "*.png" \
          --recursive --cache-control max-age=$S3_CACHE_MAX_AGE
      - name: Invalidate Cloud front distribution
        env:
          CLOUD_FRONT_DISTRIBUTION: E26Z5CS8442IIM
        run: aws cloudfront create-invalidation --distribution-id $CLOUD_FRONT_DISTRIBUTION --paths "/*"
